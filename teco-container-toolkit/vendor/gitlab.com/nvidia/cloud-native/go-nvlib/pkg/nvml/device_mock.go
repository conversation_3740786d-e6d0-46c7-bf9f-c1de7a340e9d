// Code generated by moq; DO NOT EDIT.
// github.com/matryer/moq

package nvml

import (
	"sync"
)

// Ensure, that DeviceMock does implement Device.
// If this is not the case, regenerate this file with moq.
var _ Device = &DeviceMock{}

// DeviceMock is a mock implementation of Device.
//
//	func TestSomethingThatUsesDevice(t *testing.T) {
//
//		// make and configure a mocked Device
//		mockedDevice := &DeviceMock{
//			CreateGpuInstanceWithPlacementFunc: func(gpuInstanceProfileInfo *GpuInstanceProfileInfo, gpuInstancePlacement *GpuInstancePlacement) (GpuInstance, Return) {
//				panic("mock out the CreateGpuInstanceWithPlacement method")
//			},
//			GetAttributesFunc: func() (DeviceAttributes, Return) {
//				panic("mock out the GetAttributes method")
//			},
//			GetComputeInstanceIdFunc: func() (int, Return) {
//				panic("mock out the GetComputeInstanceId method")
//			},
//			GetCudaComputeCapabilityFunc: func() (int, int, Return) {
//				panic("mock out the GetCudaComputeCapability method")
//			},
//			GetDeviceHandleFromMigDeviceHandleFunc: func() (Device, Return) {
//				panic("mock out the GetDeviceHandleFromMigDeviceHandle method")
//			},
//			GetGpuInstanceByIdFunc: func(ID int) (GpuInstance, Return) {
//				panic("mock out the GetGpuInstanceById method")
//			},
//			GetGpuInstanceIdFunc: func() (int, Return) {
//				panic("mock out the GetGpuInstanceId method")
//			},
//			GetGpuInstancePossiblePlacementsFunc: func(gpuInstanceProfileInfo *GpuInstanceProfileInfo) ([]GpuInstancePlacement, Return) {
//				panic("mock out the GetGpuInstancePossiblePlacements method")
//			},
//			GetGpuInstanceProfileInfoFunc: func(Profile int) (GpuInstanceProfileInfo, Return) {
//				panic("mock out the GetGpuInstanceProfileInfo method")
//			},
//			GetGpuInstancesFunc: func(Info *GpuInstanceProfileInfo) ([]GpuInstance, Return) {
//				panic("mock out the GetGpuInstances method")
//			},
//			GetIndexFunc: func() (int, Return) {
//				panic("mock out the GetIndex method")
//			},
//			GetMaxMigDeviceCountFunc: func() (int, Return) {
//				panic("mock out the GetMaxMigDeviceCount method")
//			},
//			GetMemoryInfoFunc: func() (Memory, Return) {
//				panic("mock out the GetMemoryInfo method")
//			},
//			GetMigDeviceHandleByIndexFunc: func(Index int) (Device, Return) {
//				panic("mock out the GetMigDeviceHandleByIndex method")
//			},
//			GetMigModeFunc: func() (int, int, Return) {
//				panic("mock out the GetMigMode method")
//			},
//			GetMinorNumberFunc: func() (int, Return) {
//				panic("mock out the GetMinorNumber method")
//			},
//			GetNameFunc: func() (string, Return) {
//				panic("mock out the GetName method")
//			},
//			GetPciInfoFunc: func() (PciInfo, Return) {
//				panic("mock out the GetPciInfo method")
//			},
//			GetSupportedEventTypesFunc: func() (uint64, Return) {
//				panic("mock out the GetSupportedEventTypes method")
//			},
//			GetUUIDFunc: func() (string, Return) {
//				panic("mock out the GetUUID method")
//			},
//			IsMigDeviceHandleFunc: func() (bool, Return) {
//				panic("mock out the IsMigDeviceHandle method")
//			},
//			RegisterEventsFunc: func(v uint64, eventSet EventSet) Return {
//				panic("mock out the RegisterEvents method")
//			},
//			SetMigModeFunc: func(Mode int) (Return, Return) {
//				panic("mock out the SetMigMode method")
//			},
//		}
//
//		// use mockedDevice in code that requires Device
//		// and then make assertions.
//
//	}
type DeviceMock struct {
	// CreateGpuInstanceWithPlacementFunc mocks the CreateGpuInstanceWithPlacement method.
	CreateGpuInstanceWithPlacementFunc func(gpuInstanceProfileInfo *GpuInstanceProfileInfo, gpuInstancePlacement *GpuInstancePlacement) (GpuInstance, Return)

	// GetAttributesFunc mocks the GetAttributes method.
	GetAttributesFunc func() (DeviceAttributes, Return)

	// GetComputeInstanceIdFunc mocks the GetComputeInstanceId method.
	GetComputeInstanceIdFunc func() (int, Return)

	// GetCudaComputeCapabilityFunc mocks the GetCudaComputeCapability method.
	GetCudaComputeCapabilityFunc func() (int, int, Return)

	// GetDeviceHandleFromMigDeviceHandleFunc mocks the GetDeviceHandleFromMigDeviceHandle method.
	GetDeviceHandleFromMigDeviceHandleFunc func() (Device, Return)

	// GetGpuInstanceByIdFunc mocks the GetGpuInstanceById method.
	GetGpuInstanceByIdFunc func(ID int) (GpuInstance, Return)

	// GetGpuInstanceIdFunc mocks the GetGpuInstanceId method.
	GetGpuInstanceIdFunc func() (int, Return)

	// GetGpuInstancePossiblePlacementsFunc mocks the GetGpuInstancePossiblePlacements method.
	GetGpuInstancePossiblePlacementsFunc func(gpuInstanceProfileInfo *GpuInstanceProfileInfo) ([]GpuInstancePlacement, Return)

	// GetGpuInstanceProfileInfoFunc mocks the GetGpuInstanceProfileInfo method.
	GetGpuInstanceProfileInfoFunc func(Profile int) (GpuInstanceProfileInfo, Return)

	// GetGpuInstancesFunc mocks the GetGpuInstances method.
	GetGpuInstancesFunc func(Info *GpuInstanceProfileInfo) ([]GpuInstance, Return)

	// GetIndexFunc mocks the GetIndex method.
	GetIndexFunc func() (int, Return)

	// GetMaxMigDeviceCountFunc mocks the GetMaxMigDeviceCount method.
	GetMaxMigDeviceCountFunc func() (int, Return)

	// GetMemoryInfoFunc mocks the GetMemoryInfo method.
	GetMemoryInfoFunc func() (Memory, Return)

	// GetMigDeviceHandleByIndexFunc mocks the GetMigDeviceHandleByIndex method.
	GetMigDeviceHandleByIndexFunc func(Index int) (Device, Return)

	// GetMigModeFunc mocks the GetMigMode method.
	GetMigModeFunc func() (int, int, Return)

	// GetMinorNumberFunc mocks the GetMinorNumber method.
	GetMinorNumberFunc func() (int, Return)

	// GetNameFunc mocks the GetName method.
	GetNameFunc func() (string, Return)

	// GetPciInfoFunc mocks the GetPciInfo method.
	GetPciInfoFunc func() (PciInfo, Return)

	// GetSupportedEventTypesFunc mocks the GetSupportedEventTypes method.
	GetSupportedEventTypesFunc func() (uint64, Return)

	// GetUUIDFunc mocks the GetUUID method.
	GetUUIDFunc func() (string, Return)

	// IsMigDeviceHandleFunc mocks the IsMigDeviceHandle method.
	IsMigDeviceHandleFunc func() (bool, Return)

	// RegisterEventsFunc mocks the RegisterEvents method.
	RegisterEventsFunc func(v uint64, eventSet EventSet) Return

	// SetMigModeFunc mocks the SetMigMode method.
	SetMigModeFunc func(Mode int) (Return, Return)

	// calls tracks calls to the methods.
	calls struct {
		// CreateGpuInstanceWithPlacement holds details about calls to the CreateGpuInstanceWithPlacement method.
		CreateGpuInstanceWithPlacement []struct {
			// GpuInstanceProfileInfo is the gpuInstanceProfileInfo argument value.
			GpuInstanceProfileInfo *GpuInstanceProfileInfo
			// GpuInstancePlacement is the gpuInstancePlacement argument value.
			GpuInstancePlacement *GpuInstancePlacement
		}
		// GetAttributes holds details about calls to the GetAttributes method.
		GetAttributes []struct {
		}
		// GetComputeInstanceId holds details about calls to the GetComputeInstanceId method.
		GetComputeInstanceId []struct {
		}
		// GetCudaComputeCapability holds details about calls to the GetCudaComputeCapability method.
		GetCudaComputeCapability []struct {
		}
		// GetDeviceHandleFromMigDeviceHandle holds details about calls to the GetDeviceHandleFromMigDeviceHandle method.
		GetDeviceHandleFromMigDeviceHandle []struct {
		}
		// GetGpuInstanceById holds details about calls to the GetGpuInstanceById method.
		GetGpuInstanceById []struct {
			// ID is the ID argument value.
			ID int
		}
		// GetGpuInstanceId holds details about calls to the GetGpuInstanceId method.
		GetGpuInstanceId []struct {
		}
		// GetGpuInstancePossiblePlacements holds details about calls to the GetGpuInstancePossiblePlacements method.
		GetGpuInstancePossiblePlacements []struct {
			// GpuInstanceProfileInfo is the gpuInstanceProfileInfo argument value.
			GpuInstanceProfileInfo *GpuInstanceProfileInfo
		}
		// GetGpuInstanceProfileInfo holds details about calls to the GetGpuInstanceProfileInfo method.
		GetGpuInstanceProfileInfo []struct {
			// Profile is the Profile argument value.
			Profile int
		}
		// GetGpuInstances holds details about calls to the GetGpuInstances method.
		GetGpuInstances []struct {
			// Info is the Info argument value.
			Info *GpuInstanceProfileInfo
		}
		// GetIndex holds details about calls to the GetIndex method.
		GetIndex []struct {
		}
		// GetMaxMigDeviceCount holds details about calls to the GetMaxMigDeviceCount method.
		GetMaxMigDeviceCount []struct {
		}
		// GetMemoryInfo holds details about calls to the GetMemoryInfo method.
		GetMemoryInfo []struct {
		}
		// GetMigDeviceHandleByIndex holds details about calls to the GetMigDeviceHandleByIndex method.
		GetMigDeviceHandleByIndex []struct {
			// Index is the Index argument value.
			Index int
		}
		// GetMigMode holds details about calls to the GetMigMode method.
		GetMigMode []struct {
		}
		// GetMinorNumber holds details about calls to the GetMinorNumber method.
		GetMinorNumber []struct {
		}
		// GetName holds details about calls to the GetName method.
		GetName []struct {
		}
		// GetPciInfo holds details about calls to the GetPciInfo method.
		GetPciInfo []struct {
		}
		// GetSupportedEventTypes holds details about calls to the GetSupportedEventTypes method.
		GetSupportedEventTypes []struct {
		}
		// GetUUID holds details about calls to the GetUUID method.
		GetUUID []struct {
		}
		// IsMigDeviceHandle holds details about calls to the IsMigDeviceHandle method.
		IsMigDeviceHandle []struct {
		}
		// RegisterEvents holds details about calls to the RegisterEvents method.
		RegisterEvents []struct {
			// V is the v argument value.
			V uint64
			// EventSet is the eventSet argument value.
			EventSet EventSet
		}
		// SetMigMode holds details about calls to the SetMigMode method.
		SetMigMode []struct {
			// Mode is the Mode argument value.
			Mode int
		}
	}
	lockCreateGpuInstanceWithPlacement     sync.RWMutex
	lockGetAttributes                      sync.RWMutex
	lockGetComputeInstanceId               sync.RWMutex
	lockGetCudaComputeCapability           sync.RWMutex
	lockGetDeviceHandleFromMigDeviceHandle sync.RWMutex
	lockGetGpuInstanceById                 sync.RWMutex
	lockGetGpuInstanceId                   sync.RWMutex
	lockGetGpuInstancePossiblePlacements   sync.RWMutex
	lockGetGpuInstanceProfileInfo          sync.RWMutex
	lockGetGpuInstances                    sync.RWMutex
	lockGetIndex                           sync.RWMutex
	lockGetMaxMigDeviceCount               sync.RWMutex
	lockGetMemoryInfo                      sync.RWMutex
	lockGetMigDeviceHandleByIndex          sync.RWMutex
	lockGetMigMode                         sync.RWMutex
	lockGetMinorNumber                     sync.RWMutex
	lockGetName                            sync.RWMutex
	lockGetPciInfo                         sync.RWMutex
	lockGetSupportedEventTypes             sync.RWMutex
	lockGetUUID                            sync.RWMutex
	lockIsMigDeviceHandle                  sync.RWMutex
	lockRegisterEvents                     sync.RWMutex
	lockSetMigMode                         sync.RWMutex
}

// CreateGpuInstanceWithPlacement calls CreateGpuInstanceWithPlacementFunc.
func (mock *DeviceMock) CreateGpuInstanceWithPlacement(gpuInstanceProfileInfo *GpuInstanceProfileInfo, gpuInstancePlacement *GpuInstancePlacement) (GpuInstance, Return) {
	if mock.CreateGpuInstanceWithPlacementFunc == nil {
		panic("DeviceMock.CreateGpuInstanceWithPlacementFunc: method is nil but Device.CreateGpuInstanceWithPlacement was just called")
	}
	callInfo := struct {
		GpuInstanceProfileInfo *GpuInstanceProfileInfo
		GpuInstancePlacement   *GpuInstancePlacement
	}{
		GpuInstanceProfileInfo: gpuInstanceProfileInfo,
		GpuInstancePlacement:   gpuInstancePlacement,
	}
	mock.lockCreateGpuInstanceWithPlacement.Lock()
	mock.calls.CreateGpuInstanceWithPlacement = append(mock.calls.CreateGpuInstanceWithPlacement, callInfo)
	mock.lockCreateGpuInstanceWithPlacement.Unlock()
	return mock.CreateGpuInstanceWithPlacementFunc(gpuInstanceProfileInfo, gpuInstancePlacement)
}

// CreateGpuInstanceWithPlacementCalls gets all the calls that were made to CreateGpuInstanceWithPlacement.
// Check the length with:
//
//	len(mockedDevice.CreateGpuInstanceWithPlacementCalls())
func (mock *DeviceMock) CreateGpuInstanceWithPlacementCalls() []struct {
	GpuInstanceProfileInfo *GpuInstanceProfileInfo
	GpuInstancePlacement   *GpuInstancePlacement
} {
	var calls []struct {
		GpuInstanceProfileInfo *GpuInstanceProfileInfo
		GpuInstancePlacement   *GpuInstancePlacement
	}
	mock.lockCreateGpuInstanceWithPlacement.RLock()
	calls = mock.calls.CreateGpuInstanceWithPlacement
	mock.lockCreateGpuInstanceWithPlacement.RUnlock()
	return calls
}

// GetAttributes calls GetAttributesFunc.
func (mock *DeviceMock) GetAttributes() (DeviceAttributes, Return) {
	if mock.GetAttributesFunc == nil {
		panic("DeviceMock.GetAttributesFunc: method is nil but Device.GetAttributes was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetAttributes.Lock()
	mock.calls.GetAttributes = append(mock.calls.GetAttributes, callInfo)
	mock.lockGetAttributes.Unlock()
	return mock.GetAttributesFunc()
}

// GetAttributesCalls gets all the calls that were made to GetAttributes.
// Check the length with:
//
//	len(mockedDevice.GetAttributesCalls())
func (mock *DeviceMock) GetAttributesCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetAttributes.RLock()
	calls = mock.calls.GetAttributes
	mock.lockGetAttributes.RUnlock()
	return calls
}

// GetComputeInstanceId calls GetComputeInstanceIdFunc.
func (mock *DeviceMock) GetComputeInstanceId() (int, Return) {
	if mock.GetComputeInstanceIdFunc == nil {
		panic("DeviceMock.GetComputeInstanceIdFunc: method is nil but Device.GetComputeInstanceId was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetComputeInstanceId.Lock()
	mock.calls.GetComputeInstanceId = append(mock.calls.GetComputeInstanceId, callInfo)
	mock.lockGetComputeInstanceId.Unlock()
	return mock.GetComputeInstanceIdFunc()
}

// GetComputeInstanceIdCalls gets all the calls that were made to GetComputeInstanceId.
// Check the length with:
//
//	len(mockedDevice.GetComputeInstanceIdCalls())
func (mock *DeviceMock) GetComputeInstanceIdCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetComputeInstanceId.RLock()
	calls = mock.calls.GetComputeInstanceId
	mock.lockGetComputeInstanceId.RUnlock()
	return calls
}

// GetCudaComputeCapability calls GetCudaComputeCapabilityFunc.
func (mock *DeviceMock) GetCudaComputeCapability() (int, int, Return) {
	if mock.GetCudaComputeCapabilityFunc == nil {
		panic("DeviceMock.GetCudaComputeCapabilityFunc: method is nil but Device.GetCudaComputeCapability was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetCudaComputeCapability.Lock()
	mock.calls.GetCudaComputeCapability = append(mock.calls.GetCudaComputeCapability, callInfo)
	mock.lockGetCudaComputeCapability.Unlock()
	return mock.GetCudaComputeCapabilityFunc()
}

// GetCudaComputeCapabilityCalls gets all the calls that were made to GetCudaComputeCapability.
// Check the length with:
//
//	len(mockedDevice.GetCudaComputeCapabilityCalls())
func (mock *DeviceMock) GetCudaComputeCapabilityCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetCudaComputeCapability.RLock()
	calls = mock.calls.GetCudaComputeCapability
	mock.lockGetCudaComputeCapability.RUnlock()
	return calls
}

// GetDeviceHandleFromMigDeviceHandle calls GetDeviceHandleFromMigDeviceHandleFunc.
func (mock *DeviceMock) GetDeviceHandleFromMigDeviceHandle() (Device, Return) {
	if mock.GetDeviceHandleFromMigDeviceHandleFunc == nil {
		panic("DeviceMock.GetDeviceHandleFromMigDeviceHandleFunc: method is nil but Device.GetDeviceHandleFromMigDeviceHandle was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetDeviceHandleFromMigDeviceHandle.Lock()
	mock.calls.GetDeviceHandleFromMigDeviceHandle = append(mock.calls.GetDeviceHandleFromMigDeviceHandle, callInfo)
	mock.lockGetDeviceHandleFromMigDeviceHandle.Unlock()
	return mock.GetDeviceHandleFromMigDeviceHandleFunc()
}

// GetDeviceHandleFromMigDeviceHandleCalls gets all the calls that were made to GetDeviceHandleFromMigDeviceHandle.
// Check the length with:
//
//	len(mockedDevice.GetDeviceHandleFromMigDeviceHandleCalls())
func (mock *DeviceMock) GetDeviceHandleFromMigDeviceHandleCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetDeviceHandleFromMigDeviceHandle.RLock()
	calls = mock.calls.GetDeviceHandleFromMigDeviceHandle
	mock.lockGetDeviceHandleFromMigDeviceHandle.RUnlock()
	return calls
}

// GetGpuInstanceById calls GetGpuInstanceByIdFunc.
func (mock *DeviceMock) GetGpuInstanceById(ID int) (GpuInstance, Return) {
	if mock.GetGpuInstanceByIdFunc == nil {
		panic("DeviceMock.GetGpuInstanceByIdFunc: method is nil but Device.GetGpuInstanceById was just called")
	}
	callInfo := struct {
		ID int
	}{
		ID: ID,
	}
	mock.lockGetGpuInstanceById.Lock()
	mock.calls.GetGpuInstanceById = append(mock.calls.GetGpuInstanceById, callInfo)
	mock.lockGetGpuInstanceById.Unlock()
	return mock.GetGpuInstanceByIdFunc(ID)
}

// GetGpuInstanceByIdCalls gets all the calls that were made to GetGpuInstanceById.
// Check the length with:
//
//	len(mockedDevice.GetGpuInstanceByIdCalls())
func (mock *DeviceMock) GetGpuInstanceByIdCalls() []struct {
	ID int
} {
	var calls []struct {
		ID int
	}
	mock.lockGetGpuInstanceById.RLock()
	calls = mock.calls.GetGpuInstanceById
	mock.lockGetGpuInstanceById.RUnlock()
	return calls
}

// GetGpuInstanceId calls GetGpuInstanceIdFunc.
func (mock *DeviceMock) GetGpuInstanceId() (int, Return) {
	if mock.GetGpuInstanceIdFunc == nil {
		panic("DeviceMock.GetGpuInstanceIdFunc: method is nil but Device.GetGpuInstanceId was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetGpuInstanceId.Lock()
	mock.calls.GetGpuInstanceId = append(mock.calls.GetGpuInstanceId, callInfo)
	mock.lockGetGpuInstanceId.Unlock()
	return mock.GetGpuInstanceIdFunc()
}

// GetGpuInstanceIdCalls gets all the calls that were made to GetGpuInstanceId.
// Check the length with:
//
//	len(mockedDevice.GetGpuInstanceIdCalls())
func (mock *DeviceMock) GetGpuInstanceIdCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetGpuInstanceId.RLock()
	calls = mock.calls.GetGpuInstanceId
	mock.lockGetGpuInstanceId.RUnlock()
	return calls
}

// GetGpuInstancePossiblePlacements calls GetGpuInstancePossiblePlacementsFunc.
func (mock *DeviceMock) GetGpuInstancePossiblePlacements(gpuInstanceProfileInfo *GpuInstanceProfileInfo) ([]GpuInstancePlacement, Return) {
	if mock.GetGpuInstancePossiblePlacementsFunc == nil {
		panic("DeviceMock.GetGpuInstancePossiblePlacementsFunc: method is nil but Device.GetGpuInstancePossiblePlacements was just called")
	}
	callInfo := struct {
		GpuInstanceProfileInfo *GpuInstanceProfileInfo
	}{
		GpuInstanceProfileInfo: gpuInstanceProfileInfo,
	}
	mock.lockGetGpuInstancePossiblePlacements.Lock()
	mock.calls.GetGpuInstancePossiblePlacements = append(mock.calls.GetGpuInstancePossiblePlacements, callInfo)
	mock.lockGetGpuInstancePossiblePlacements.Unlock()
	return mock.GetGpuInstancePossiblePlacementsFunc(gpuInstanceProfileInfo)
}

// GetGpuInstancePossiblePlacementsCalls gets all the calls that were made to GetGpuInstancePossiblePlacements.
// Check the length with:
//
//	len(mockedDevice.GetGpuInstancePossiblePlacementsCalls())
func (mock *DeviceMock) GetGpuInstancePossiblePlacementsCalls() []struct {
	GpuInstanceProfileInfo *GpuInstanceProfileInfo
} {
	var calls []struct {
		GpuInstanceProfileInfo *GpuInstanceProfileInfo
	}
	mock.lockGetGpuInstancePossiblePlacements.RLock()
	calls = mock.calls.GetGpuInstancePossiblePlacements
	mock.lockGetGpuInstancePossiblePlacements.RUnlock()
	return calls
}

// GetGpuInstanceProfileInfo calls GetGpuInstanceProfileInfoFunc.
func (mock *DeviceMock) GetGpuInstanceProfileInfo(Profile int) (GpuInstanceProfileInfo, Return) {
	if mock.GetGpuInstanceProfileInfoFunc == nil {
		panic("DeviceMock.GetGpuInstanceProfileInfoFunc: method is nil but Device.GetGpuInstanceProfileInfo was just called")
	}
	callInfo := struct {
		Profile int
	}{
		Profile: Profile,
	}
	mock.lockGetGpuInstanceProfileInfo.Lock()
	mock.calls.GetGpuInstanceProfileInfo = append(mock.calls.GetGpuInstanceProfileInfo, callInfo)
	mock.lockGetGpuInstanceProfileInfo.Unlock()
	return mock.GetGpuInstanceProfileInfoFunc(Profile)
}

// GetGpuInstanceProfileInfoCalls gets all the calls that were made to GetGpuInstanceProfileInfo.
// Check the length with:
//
//	len(mockedDevice.GetGpuInstanceProfileInfoCalls())
func (mock *DeviceMock) GetGpuInstanceProfileInfoCalls() []struct {
	Profile int
} {
	var calls []struct {
		Profile int
	}
	mock.lockGetGpuInstanceProfileInfo.RLock()
	calls = mock.calls.GetGpuInstanceProfileInfo
	mock.lockGetGpuInstanceProfileInfo.RUnlock()
	return calls
}

// GetGpuInstances calls GetGpuInstancesFunc.
func (mock *DeviceMock) GetGpuInstances(Info *GpuInstanceProfileInfo) ([]GpuInstance, Return) {
	if mock.GetGpuInstancesFunc == nil {
		panic("DeviceMock.GetGpuInstancesFunc: method is nil but Device.GetGpuInstances was just called")
	}
	callInfo := struct {
		Info *GpuInstanceProfileInfo
	}{
		Info: Info,
	}
	mock.lockGetGpuInstances.Lock()
	mock.calls.GetGpuInstances = append(mock.calls.GetGpuInstances, callInfo)
	mock.lockGetGpuInstances.Unlock()
	return mock.GetGpuInstancesFunc(Info)
}

// GetGpuInstancesCalls gets all the calls that were made to GetGpuInstances.
// Check the length with:
//
//	len(mockedDevice.GetGpuInstancesCalls())
func (mock *DeviceMock) GetGpuInstancesCalls() []struct {
	Info *GpuInstanceProfileInfo
} {
	var calls []struct {
		Info *GpuInstanceProfileInfo
	}
	mock.lockGetGpuInstances.RLock()
	calls = mock.calls.GetGpuInstances
	mock.lockGetGpuInstances.RUnlock()
	return calls
}

// GetIndex calls GetIndexFunc.
func (mock *DeviceMock) GetIndex() (int, Return) {
	if mock.GetIndexFunc == nil {
		panic("DeviceMock.GetIndexFunc: method is nil but Device.GetIndex was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetIndex.Lock()
	mock.calls.GetIndex = append(mock.calls.GetIndex, callInfo)
	mock.lockGetIndex.Unlock()
	return mock.GetIndexFunc()
}

// GetIndexCalls gets all the calls that were made to GetIndex.
// Check the length with:
//
//	len(mockedDevice.GetIndexCalls())
func (mock *DeviceMock) GetIndexCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetIndex.RLock()
	calls = mock.calls.GetIndex
	mock.lockGetIndex.RUnlock()
	return calls
}

// GetMaxMigDeviceCount calls GetMaxMigDeviceCountFunc.
func (mock *DeviceMock) GetMaxMigDeviceCount() (int, Return) {
	if mock.GetMaxMigDeviceCountFunc == nil {
		panic("DeviceMock.GetMaxMigDeviceCountFunc: method is nil but Device.GetMaxMigDeviceCount was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetMaxMigDeviceCount.Lock()
	mock.calls.GetMaxMigDeviceCount = append(mock.calls.GetMaxMigDeviceCount, callInfo)
	mock.lockGetMaxMigDeviceCount.Unlock()
	return mock.GetMaxMigDeviceCountFunc()
}

// GetMaxMigDeviceCountCalls gets all the calls that were made to GetMaxMigDeviceCount.
// Check the length with:
//
//	len(mockedDevice.GetMaxMigDeviceCountCalls())
func (mock *DeviceMock) GetMaxMigDeviceCountCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetMaxMigDeviceCount.RLock()
	calls = mock.calls.GetMaxMigDeviceCount
	mock.lockGetMaxMigDeviceCount.RUnlock()
	return calls
}

// GetMemoryInfo calls GetMemoryInfoFunc.
func (mock *DeviceMock) GetMemoryInfo() (Memory, Return) {
	if mock.GetMemoryInfoFunc == nil {
		panic("DeviceMock.GetMemoryInfoFunc: method is nil but Device.GetMemoryInfo was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetMemoryInfo.Lock()
	mock.calls.GetMemoryInfo = append(mock.calls.GetMemoryInfo, callInfo)
	mock.lockGetMemoryInfo.Unlock()
	return mock.GetMemoryInfoFunc()
}

// GetMemoryInfoCalls gets all the calls that were made to GetMemoryInfo.
// Check the length with:
//
//	len(mockedDevice.GetMemoryInfoCalls())
func (mock *DeviceMock) GetMemoryInfoCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetMemoryInfo.RLock()
	calls = mock.calls.GetMemoryInfo
	mock.lockGetMemoryInfo.RUnlock()
	return calls
}

// GetMigDeviceHandleByIndex calls GetMigDeviceHandleByIndexFunc.
func (mock *DeviceMock) GetMigDeviceHandleByIndex(Index int) (Device, Return) {
	if mock.GetMigDeviceHandleByIndexFunc == nil {
		panic("DeviceMock.GetMigDeviceHandleByIndexFunc: method is nil but Device.GetMigDeviceHandleByIndex was just called")
	}
	callInfo := struct {
		Index int
	}{
		Index: Index,
	}
	mock.lockGetMigDeviceHandleByIndex.Lock()
	mock.calls.GetMigDeviceHandleByIndex = append(mock.calls.GetMigDeviceHandleByIndex, callInfo)
	mock.lockGetMigDeviceHandleByIndex.Unlock()
	return mock.GetMigDeviceHandleByIndexFunc(Index)
}

// GetMigDeviceHandleByIndexCalls gets all the calls that were made to GetMigDeviceHandleByIndex.
// Check the length with:
//
//	len(mockedDevice.GetMigDeviceHandleByIndexCalls())
func (mock *DeviceMock) GetMigDeviceHandleByIndexCalls() []struct {
	Index int
} {
	var calls []struct {
		Index int
	}
	mock.lockGetMigDeviceHandleByIndex.RLock()
	calls = mock.calls.GetMigDeviceHandleByIndex
	mock.lockGetMigDeviceHandleByIndex.RUnlock()
	return calls
}

// GetMigMode calls GetMigModeFunc.
func (mock *DeviceMock) GetMigMode() (int, int, Return) {
	if mock.GetMigModeFunc == nil {
		panic("DeviceMock.GetMigModeFunc: method is nil but Device.GetMigMode was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetMigMode.Lock()
	mock.calls.GetMigMode = append(mock.calls.GetMigMode, callInfo)
	mock.lockGetMigMode.Unlock()
	return mock.GetMigModeFunc()
}

// GetMigModeCalls gets all the calls that were made to GetMigMode.
// Check the length with:
//
//	len(mockedDevice.GetMigModeCalls())
func (mock *DeviceMock) GetMigModeCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetMigMode.RLock()
	calls = mock.calls.GetMigMode
	mock.lockGetMigMode.RUnlock()
	return calls
}

// GetMinorNumber calls GetMinorNumberFunc.
func (mock *DeviceMock) GetMinorNumber() (int, Return) {
	if mock.GetMinorNumberFunc == nil {
		panic("DeviceMock.GetMinorNumberFunc: method is nil but Device.GetMinorNumber was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetMinorNumber.Lock()
	mock.calls.GetMinorNumber = append(mock.calls.GetMinorNumber, callInfo)
	mock.lockGetMinorNumber.Unlock()
	return mock.GetMinorNumberFunc()
}

// GetMinorNumberCalls gets all the calls that were made to GetMinorNumber.
// Check the length with:
//
//	len(mockedDevice.GetMinorNumberCalls())
func (mock *DeviceMock) GetMinorNumberCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetMinorNumber.RLock()
	calls = mock.calls.GetMinorNumber
	mock.lockGetMinorNumber.RUnlock()
	return calls
}

// GetName calls GetNameFunc.
func (mock *DeviceMock) GetName() (string, Return) {
	if mock.GetNameFunc == nil {
		panic("DeviceMock.GetNameFunc: method is nil but Device.GetName was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetName.Lock()
	mock.calls.GetName = append(mock.calls.GetName, callInfo)
	mock.lockGetName.Unlock()
	return mock.GetNameFunc()
}

// GetNameCalls gets all the calls that were made to GetName.
// Check the length with:
//
//	len(mockedDevice.GetNameCalls())
func (mock *DeviceMock) GetNameCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetName.RLock()
	calls = mock.calls.GetName
	mock.lockGetName.RUnlock()
	return calls
}

// GetPciInfo calls GetPciInfoFunc.
func (mock *DeviceMock) GetPciInfo() (PciInfo, Return) {
	if mock.GetPciInfoFunc == nil {
		panic("DeviceMock.GetPciInfoFunc: method is nil but Device.GetPciInfo was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetPciInfo.Lock()
	mock.calls.GetPciInfo = append(mock.calls.GetPciInfo, callInfo)
	mock.lockGetPciInfo.Unlock()
	return mock.GetPciInfoFunc()
}

// GetPciInfoCalls gets all the calls that were made to GetPciInfo.
// Check the length with:
//
//	len(mockedDevice.GetPciInfoCalls())
func (mock *DeviceMock) GetPciInfoCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetPciInfo.RLock()
	calls = mock.calls.GetPciInfo
	mock.lockGetPciInfo.RUnlock()
	return calls
}

// GetSupportedEventTypes calls GetSupportedEventTypesFunc.
func (mock *DeviceMock) GetSupportedEventTypes() (uint64, Return) {
	if mock.GetSupportedEventTypesFunc == nil {
		panic("DeviceMock.GetSupportedEventTypesFunc: method is nil but Device.GetSupportedEventTypes was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetSupportedEventTypes.Lock()
	mock.calls.GetSupportedEventTypes = append(mock.calls.GetSupportedEventTypes, callInfo)
	mock.lockGetSupportedEventTypes.Unlock()
	return mock.GetSupportedEventTypesFunc()
}

// GetSupportedEventTypesCalls gets all the calls that were made to GetSupportedEventTypes.
// Check the length with:
//
//	len(mockedDevice.GetSupportedEventTypesCalls())
func (mock *DeviceMock) GetSupportedEventTypesCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetSupportedEventTypes.RLock()
	calls = mock.calls.GetSupportedEventTypes
	mock.lockGetSupportedEventTypes.RUnlock()
	return calls
}

// GetUUID calls GetUUIDFunc.
func (mock *DeviceMock) GetUUID() (string, Return) {
	if mock.GetUUIDFunc == nil {
		panic("DeviceMock.GetUUIDFunc: method is nil but Device.GetUUID was just called")
	}
	callInfo := struct {
	}{}
	mock.lockGetUUID.Lock()
	mock.calls.GetUUID = append(mock.calls.GetUUID, callInfo)
	mock.lockGetUUID.Unlock()
	return mock.GetUUIDFunc()
}

// GetUUIDCalls gets all the calls that were made to GetUUID.
// Check the length with:
//
//	len(mockedDevice.GetUUIDCalls())
func (mock *DeviceMock) GetUUIDCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockGetUUID.RLock()
	calls = mock.calls.GetUUID
	mock.lockGetUUID.RUnlock()
	return calls
}

// IsMigDeviceHandle calls IsMigDeviceHandleFunc.
func (mock *DeviceMock) IsMigDeviceHandle() (bool, Return) {
	if mock.IsMigDeviceHandleFunc == nil {
		panic("DeviceMock.IsMigDeviceHandleFunc: method is nil but Device.IsMigDeviceHandle was just called")
	}
	callInfo := struct {
	}{}
	mock.lockIsMigDeviceHandle.Lock()
	mock.calls.IsMigDeviceHandle = append(mock.calls.IsMigDeviceHandle, callInfo)
	mock.lockIsMigDeviceHandle.Unlock()
	return mock.IsMigDeviceHandleFunc()
}

// IsMigDeviceHandleCalls gets all the calls that were made to IsMigDeviceHandle.
// Check the length with:
//
//	len(mockedDevice.IsMigDeviceHandleCalls())
func (mock *DeviceMock) IsMigDeviceHandleCalls() []struct {
} {
	var calls []struct {
	}
	mock.lockIsMigDeviceHandle.RLock()
	calls = mock.calls.IsMigDeviceHandle
	mock.lockIsMigDeviceHandle.RUnlock()
	return calls
}

// RegisterEvents calls RegisterEventsFunc.
func (mock *DeviceMock) RegisterEvents(v uint64, eventSet EventSet) Return {
	if mock.RegisterEventsFunc == nil {
		panic("DeviceMock.RegisterEventsFunc: method is nil but Device.RegisterEvents was just called")
	}
	callInfo := struct {
		V        uint64
		EventSet EventSet
	}{
		V:        v,
		EventSet: eventSet,
	}
	mock.lockRegisterEvents.Lock()
	mock.calls.RegisterEvents = append(mock.calls.RegisterEvents, callInfo)
	mock.lockRegisterEvents.Unlock()
	return mock.RegisterEventsFunc(v, eventSet)
}

// RegisterEventsCalls gets all the calls that were made to RegisterEvents.
// Check the length with:
//
//	len(mockedDevice.RegisterEventsCalls())
func (mock *DeviceMock) RegisterEventsCalls() []struct {
	V        uint64
	EventSet EventSet
} {
	var calls []struct {
		V        uint64
		EventSet EventSet
	}
	mock.lockRegisterEvents.RLock()
	calls = mock.calls.RegisterEvents
	mock.lockRegisterEvents.RUnlock()
	return calls
}

// SetMigMode calls SetMigModeFunc.
func (mock *DeviceMock) SetMigMode(Mode int) (Return, Return) {
	if mock.SetMigModeFunc == nil {
		panic("DeviceMock.SetMigModeFunc: method is nil but Device.SetMigMode was just called")
	}
	callInfo := struct {
		Mode int
	}{
		Mode: Mode,
	}
	mock.lockSetMigMode.Lock()
	mock.calls.SetMigMode = append(mock.calls.SetMigMode, callInfo)
	mock.lockSetMigMode.Unlock()
	return mock.SetMigModeFunc(Mode)
}

// SetMigModeCalls gets all the calls that were made to SetMigMode.
// Check the length with:
//
//	len(mockedDevice.SetMigModeCalls())
func (mock *DeviceMock) SetMigModeCalls() []struct {
	Mode int
} {
	var calls []struct {
		Mode int
	}
	mock.lockSetMigMode.RLock()
	calls = mock.calls.SetMigMode
	mock.lockSetMigMode.RUnlock()
	return calls
}
