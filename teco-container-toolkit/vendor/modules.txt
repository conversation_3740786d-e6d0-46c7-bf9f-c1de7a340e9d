# github.com/BurntSushi/toml v1.0.0
## explicit; go 1.16
github.com/BurntSushi/toml
github.com/BurntSushi/toml/internal
# github.com/NVIDIA/go-nvml v0.11.6-0.0.20220823120812-7e2082095e82
## explicit; go 1.15
github.com/NVIDIA/go-nvml/pkg/dl
github.com/NVIDIA/go-nvml/pkg/nvml
# github.com/container-orchestrated-devices/container-device-interface v0.5.4-0.20230111111500-5b3b5d81179a
## explicit; go 1.17
github.com/container-orchestrated-devices/container-device-interface/internal/multierror
github.com/container-orchestrated-devices/container-device-interface/pkg/cdi
github.com/container-orchestrated-devices/container-device-interface/specs-go
# github.com/cpuguy83/go-md2man/v2 v2.0.1
## explicit; go 1.11
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/fsnotify/fsnotify v1.5.4
## explicit; go 1.16
github.com/fsnotify/fsnotify
# github.com/hashicorp/errwrap v1.1.0
## explicit
# github.com/kr/text v0.2.0
## explicit
# github.com/opencontainers/runc v1.1.4
## explicit; go 1.16
github.com/opencontainers/runc/libcontainer/devices
# github.com/opencontainers/runtime-spec v1.0.3-0.20220825212826-86290f6a00fb
## explicit
github.com/opencontainers/runtime-spec/specs-go
# github.com/opencontainers/runtime-tools v0.9.1-0.20221107090550-2e043c6bd626
## explicit; go 1.16
github.com/opencontainers/runtime-tools/generate
github.com/opencontainers/runtime-tools/generate/seccomp
github.com/opencontainers/runtime-tools/validate/capabilities
# github.com/opencontainers/selinux v1.10.1
## explicit; go 1.13
# github.com/pelletier/go-toml v1.9.4
## explicit; go 1.12
github.com/pelletier/go-toml
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/sirupsen/logrus v1.9.0
## explicit; go 1.13
github.com/sirupsen/logrus
github.com/sirupsen/logrus/hooks/test
# github.com/stretchr/testify v1.7.0
## explicit; go 1.13
github.com/stretchr/testify/assert
github.com/stretchr/testify/require
# github.com/syndtr/gocapability v0.0.0-20200815063812-42c35b437635
## explicit
github.com/syndtr/gocapability/capability
# github.com/urfave/cli/v2 v2.3.0
## explicit; go 1.11
github.com/urfave/cli/v2
# github.com/xeipuuv/gojsonpointer v0.0.0-20190905194746-02993c407bfb
## explicit
# gitlab.com/nvidia/cloud-native/go-nvlib v0.0.0-20230119114711-6fe07bb33342
## explicit; go 1.16
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvlib/device
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvlib/info
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvml
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvpci
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvpci/bytes
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/nvpci/mmio
gitlab.com/nvidia/cloud-native/go-nvlib/pkg/pciids
# golang.org/x/mod v0.5.0
## explicit; go 1.17
golang.org/x/mod/semver
# golang.org/x/sys v0.0.0-20220927170352-d9d178bc13c6
## explicit; go 1.17
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/unix
golang.org/x/sys/windows
# gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c
## explicit; go 1.11
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# sigs.k8s.io/yaml v1.3.0
## explicit; go 1.12
sigs.k8s.io/yaml
