// Copyright (c) 2020, NVIDIA CORPORATION. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// WARNING: THIS FILE WAS AUTOMATICALLY GENERATED.
// Code generated by https://git.io/c-for-go. DO NOT EDIT.

package nvml

/*
#cgo LDFLAGS: -Wl,--unresolved-symbols=ignore-in-object-files
#cgo CFLAGS: -DNVML_NO_UNVERSIONED_FUNC_DEFS=1
#include "nvml.h"
#include <stdlib.h>
#include "cgo_helpers.h"
*/
import "C"
import "unsafe"

// nvmlInit_v2 function as declared in nvml/nvml.h
func nvmlInit_v2() Return {
	__ret := C.nvmlInit_v2()
	__v := (Return)(__ret)
	return __v
}

// nvmlInitWithFlags function as declared in nvml/nvml.h
func nvmlInitWithFlags(Flags uint32) Return {
	cFlags, _ := (C.uint)(Flags), cgoAllocsUnknown
	__ret := C.nvmlInitWithFlags(cFlags)
	__v := (Return)(__ret)
	return __v
}

// nvmlShutdown function as declared in nvml/nvml.h
func nvmlShutdown() Return {
	__ret := C.nvmlShutdown()
	__v := (Return)(__ret)
	return __v
}

// nvmlErrorString function as declared in nvml/nvml.h
func nvmlErrorString(Result Return) string {
	cResult, _ := (C.nvmlReturn_t)(Result), cgoAllocsUnknown
	__ret := C.nvmlErrorString(cResult)
	__v := packPCharString(__ret)
	return __v
}

// nvmlSystemGetDriverVersion function as declared in nvml/nvml.h
func nvmlSystemGetDriverVersion(Version *byte, Length uint32) Return {
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlSystemGetDriverVersion(cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetNVMLVersion function as declared in nvml/nvml.h
func nvmlSystemGetNVMLVersion(Version *byte, Length uint32) Return {
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlSystemGetNVMLVersion(cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetCudaDriverVersion function as declared in nvml/nvml.h
func nvmlSystemGetCudaDriverVersion(CudaDriverVersion *int32) Return {
	cCudaDriverVersion, _ := (*C.int)(unsafe.Pointer(CudaDriverVersion)), cgoAllocsUnknown
	__ret := C.nvmlSystemGetCudaDriverVersion(cCudaDriverVersion)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetCudaDriverVersion_v2 function as declared in nvml/nvml.h
func nvmlSystemGetCudaDriverVersion_v2(CudaDriverVersion *int32) Return {
	cCudaDriverVersion, _ := (*C.int)(unsafe.Pointer(CudaDriverVersion)), cgoAllocsUnknown
	__ret := C.nvmlSystemGetCudaDriverVersion_v2(cCudaDriverVersion)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetProcessName function as declared in nvml/nvml.h
func nvmlSystemGetProcessName(Pid uint32, Name *byte, Length uint32) Return {
	cPid, _ := (C.uint)(Pid), cgoAllocsUnknown
	cName, _ := (*C.char)(unsafe.Pointer(Name)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlSystemGetProcessName(cPid, cName, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetCount function as declared in nvml/nvml.h
func nvmlUnitGetCount(UnitCount *uint32) Return {
	cUnitCount, _ := (*C.uint)(unsafe.Pointer(UnitCount)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetCount(cUnitCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetHandleByIndex function as declared in nvml/nvml.h
func nvmlUnitGetHandleByIndex(Index uint32, Unit *Unit) Return {
	cIndex, _ := (C.uint)(Index), cgoAllocsUnknown
	cUnit, _ := (*C.nvmlUnit_t)(unsafe.Pointer(Unit)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetHandleByIndex(cIndex, cUnit)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetUnitInfo function as declared in nvml/nvml.h
func nvmlUnitGetUnitInfo(Unit Unit, Info *UnitInfo) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlUnitInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetUnitInfo(cUnit, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetLedState function as declared in nvml/nvml.h
func nvmlUnitGetLedState(Unit Unit, State *LedState) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cState, _ := (*C.nvmlLedState_t)(unsafe.Pointer(State)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetLedState(cUnit, cState)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetPsuInfo function as declared in nvml/nvml.h
func nvmlUnitGetPsuInfo(Unit Unit, Psu *PSUInfo) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cPsu, _ := (*C.nvmlPSUInfo_t)(unsafe.Pointer(Psu)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetPsuInfo(cUnit, cPsu)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetTemperature function as declared in nvml/nvml.h
func nvmlUnitGetTemperature(Unit Unit, _type uint32, Temp *uint32) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	c_type, _ := (C.uint)(_type), cgoAllocsUnknown
	cTemp, _ := (*C.uint)(unsafe.Pointer(Temp)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetTemperature(cUnit, c_type, cTemp)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetFanSpeedInfo function as declared in nvml/nvml.h
func nvmlUnitGetFanSpeedInfo(Unit Unit, FanSpeeds *UnitFanSpeeds) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cFanSpeeds, _ := (*C.nvmlUnitFanSpeeds_t)(unsafe.Pointer(FanSpeeds)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetFanSpeedInfo(cUnit, cFanSpeeds)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitGetDevices function as declared in nvml/nvml.h
func nvmlUnitGetDevices(Unit Unit, DeviceCount *uint32, Devices *Device) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cDeviceCount, _ := (*C.uint)(unsafe.Pointer(DeviceCount)), cgoAllocsUnknown
	cDevices, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Devices)), cgoAllocsUnknown
	__ret := C.nvmlUnitGetDevices(cUnit, cDeviceCount, cDevices)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetHicVersion function as declared in nvml/nvml.h
func nvmlSystemGetHicVersion(HwbcCount *uint32, HwbcEntries *HwbcEntry) Return {
	cHwbcCount, _ := (*C.uint)(unsafe.Pointer(HwbcCount)), cgoAllocsUnknown
	cHwbcEntries, _ := (*C.nvmlHwbcEntry_t)(unsafe.Pointer(HwbcEntries)), cgoAllocsUnknown
	__ret := C.nvmlSystemGetHicVersion(cHwbcCount, cHwbcEntries)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCount_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetCount_v2(DeviceCount *uint32) Return {
	cDeviceCount, _ := (*C.uint)(unsafe.Pointer(DeviceCount)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCount_v2(cDeviceCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAttributes_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetAttributes_v2(Device Device, Attributes *DeviceAttributes) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cAttributes, _ := (*C.nvmlDeviceAttributes_t)(unsafe.Pointer(Attributes)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAttributes_v2(cDevice, cAttributes)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleByIndex_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetHandleByIndex_v2(Index uint32, Device *Device) Return {
	cIndex, _ := (C.uint)(Index), cgoAllocsUnknown
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleByIndex_v2(cIndex, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleBySerial function as declared in nvml/nvml.h
func nvmlDeviceGetHandleBySerial(Serial string, Device *Device) Return {
	cSerial, _ := unpackPCharString(Serial)
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleBySerial(cSerial, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleByUUID function as declared in nvml/nvml.h
func nvmlDeviceGetHandleByUUID(Uuid string, Device *Device) Return {
	cUuid, _ := unpackPCharString(Uuid)
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleByUUID(cUuid, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleByPciBusId_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetHandleByPciBusId_v2(PciBusId string, Device *Device) Return {
	cPciBusId, _ := unpackPCharString(PciBusId)
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleByPciBusId_v2(cPciBusId, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetName function as declared in nvml/nvml.h
func nvmlDeviceGetName(Device Device, Name *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cName, _ := (*C.char)(unsafe.Pointer(Name)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetName(cDevice, cName, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBrand function as declared in nvml/nvml.h
func nvmlDeviceGetBrand(Device Device, _type *BrandType) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	c_type, _ := (*C.nvmlBrandType_t)(unsafe.Pointer(_type)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBrand(cDevice, c_type)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetIndex function as declared in nvml/nvml.h
func nvmlDeviceGetIndex(Device Device, Index *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIndex, _ := (*C.uint)(unsafe.Pointer(Index)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetIndex(cDevice, cIndex)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSerial function as declared in nvml/nvml.h
func nvmlDeviceGetSerial(Device Device, Serial *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSerial, _ := (*C.char)(unsafe.Pointer(Serial)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSerial(cDevice, cSerial, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMemoryAffinity function as declared in nvml/nvml.h
func nvmlDeviceGetMemoryAffinity(Device Device, NodeSetSize uint32, NodeSet *uint, Scope AffinityScope) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cNodeSetSize, _ := (C.uint)(NodeSetSize), cgoAllocsUnknown
	cNodeSet, _ := (*C.ulong)(unsafe.Pointer(NodeSet)), cgoAllocsUnknown
	cScope, _ := (C.nvmlAffinityScope_t)(Scope), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMemoryAffinity(cDevice, cNodeSetSize, cNodeSet, cScope)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCpuAffinityWithinScope function as declared in nvml/nvml.h
func nvmlDeviceGetCpuAffinityWithinScope(Device Device, CpuSetSize uint32, CpuSet *uint, Scope AffinityScope) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCpuSetSize, _ := (C.uint)(CpuSetSize), cgoAllocsUnknown
	cCpuSet, _ := (*C.ulong)(unsafe.Pointer(CpuSet)), cgoAllocsUnknown
	cScope, _ := (C.nvmlAffinityScope_t)(Scope), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCpuAffinityWithinScope(cDevice, cCpuSetSize, cCpuSet, cScope)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCpuAffinity function as declared in nvml/nvml.h
func nvmlDeviceGetCpuAffinity(Device Device, CpuSetSize uint32, CpuSet *uint) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCpuSetSize, _ := (C.uint)(CpuSetSize), cgoAllocsUnknown
	cCpuSet, _ := (*C.ulong)(unsafe.Pointer(CpuSet)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCpuAffinity(cDevice, cCpuSetSize, cCpuSet)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetCpuAffinity function as declared in nvml/nvml.h
func nvmlDeviceSetCpuAffinity(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetCpuAffinity(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceClearCpuAffinity function as declared in nvml/nvml.h
func nvmlDeviceClearCpuAffinity(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceClearCpuAffinity(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTopologyCommonAncestor function as declared in nvml/nvml.h
func nvmlDeviceGetTopologyCommonAncestor(Device1 Device, Device2 Device, PathInfo *GpuTopologyLevel) Return {
	cDevice1, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device1)), cgoAllocsUnknown
	cDevice2, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device2)), cgoAllocsUnknown
	cPathInfo, _ := (*C.nvmlGpuTopologyLevel_t)(unsafe.Pointer(PathInfo)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTopologyCommonAncestor(cDevice1, cDevice2, cPathInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTopologyNearestGpus function as declared in nvml/nvml.h
func nvmlDeviceGetTopologyNearestGpus(Device Device, Level GpuTopologyLevel, Count *uint32, DeviceArray *Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLevel, _ := (C.nvmlGpuTopologyLevel_t)(Level), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cDeviceArray, _ := (*C.nvmlDevice_t)(unsafe.Pointer(DeviceArray)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTopologyNearestGpus(cDevice, cLevel, cCount, cDeviceArray)
	__v := (Return)(__ret)
	return __v
}

// nvmlSystemGetTopologyGpuSet function as declared in nvml/nvml.h
func nvmlSystemGetTopologyGpuSet(CpuNumber uint32, Count *uint32, DeviceArray *Device) Return {
	cCpuNumber, _ := (C.uint)(CpuNumber), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cDeviceArray, _ := (*C.nvmlDevice_t)(unsafe.Pointer(DeviceArray)), cgoAllocsUnknown
	__ret := C.nvmlSystemGetTopologyGpuSet(cCpuNumber, cCount, cDeviceArray)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetP2PStatus function as declared in nvml/nvml.h
func nvmlDeviceGetP2PStatus(Device1 Device, Device2 Device, P2pIndex GpuP2PCapsIndex, P2pStatus *GpuP2PStatus) Return {
	cDevice1, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device1)), cgoAllocsUnknown
	cDevice2, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device2)), cgoAllocsUnknown
	cP2pIndex, _ := (C.nvmlGpuP2PCapsIndex_t)(P2pIndex), cgoAllocsUnknown
	cP2pStatus, _ := (*C.nvmlGpuP2PStatus_t)(unsafe.Pointer(P2pStatus)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetP2PStatus(cDevice1, cDevice2, cP2pIndex, cP2pStatus)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetUUID function as declared in nvml/nvml.h
func nvmlDeviceGetUUID(Device Device, Uuid *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cUuid, _ := (*C.char)(unsafe.Pointer(Uuid)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetUUID(cDevice, cUuid, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetMdevUUID function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetMdevUUID(VgpuInstance VgpuInstance, MdevUuid *byte, Size uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cMdevUuid, _ := (*C.char)(unsafe.Pointer(MdevUuid)), cgoAllocsUnknown
	cSize, _ := (C.uint)(Size), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetMdevUUID(cVgpuInstance, cMdevUuid, cSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMinorNumber function as declared in nvml/nvml.h
func nvmlDeviceGetMinorNumber(Device Device, MinorNumber *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMinorNumber, _ := (*C.uint)(unsafe.Pointer(MinorNumber)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMinorNumber(cDevice, cMinorNumber)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBoardPartNumber function as declared in nvml/nvml.h
func nvmlDeviceGetBoardPartNumber(Device Device, PartNumber *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPartNumber, _ := (*C.char)(unsafe.Pointer(PartNumber)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBoardPartNumber(cDevice, cPartNumber, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetInforomVersion function as declared in nvml/nvml.h
func nvmlDeviceGetInforomVersion(Device Device, Object InforomObject, Version *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cObject, _ := (C.nvmlInforomObject_t)(Object), cgoAllocsUnknown
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetInforomVersion(cDevice, cObject, cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetInforomImageVersion function as declared in nvml/nvml.h
func nvmlDeviceGetInforomImageVersion(Device Device, Version *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetInforomImageVersion(cDevice, cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetInforomConfigurationChecksum function as declared in nvml/nvml.h
func nvmlDeviceGetInforomConfigurationChecksum(Device Device, Checksum *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cChecksum, _ := (*C.uint)(unsafe.Pointer(Checksum)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetInforomConfigurationChecksum(cDevice, cChecksum)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceValidateInforom function as declared in nvml/nvml.h
func nvmlDeviceValidateInforom(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceValidateInforom(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDisplayMode function as declared in nvml/nvml.h
func nvmlDeviceGetDisplayMode(Device Device, Display *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cDisplay, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Display)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDisplayMode(cDevice, cDisplay)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDisplayActive function as declared in nvml/nvml.h
func nvmlDeviceGetDisplayActive(Device Device, IsActive *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIsActive, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(IsActive)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDisplayActive(cDevice, cIsActive)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPersistenceMode function as declared in nvml/nvml.h
func nvmlDeviceGetPersistenceMode(Device Device, Mode *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Mode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPersistenceMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPciInfo_v3 function as declared in nvml/nvml.h
func nvmlDeviceGetPciInfo_v3(Device Device, Pci *PciInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPci, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(Pci)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPciInfo_v3(cDevice, cPci)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMaxPcieLinkGeneration function as declared in nvml/nvml.h
func nvmlDeviceGetMaxPcieLinkGeneration(Device Device, MaxLinkGen *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMaxLinkGen, _ := (*C.uint)(unsafe.Pointer(MaxLinkGen)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMaxPcieLinkGeneration(cDevice, cMaxLinkGen)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMaxPcieLinkWidth function as declared in nvml/nvml.h
func nvmlDeviceGetMaxPcieLinkWidth(Device Device, MaxLinkWidth *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMaxLinkWidth, _ := (*C.uint)(unsafe.Pointer(MaxLinkWidth)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMaxPcieLinkWidth(cDevice, cMaxLinkWidth)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCurrPcieLinkGeneration function as declared in nvml/nvml.h
func nvmlDeviceGetCurrPcieLinkGeneration(Device Device, CurrLinkGen *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrLinkGen, _ := (*C.uint)(unsafe.Pointer(CurrLinkGen)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCurrPcieLinkGeneration(cDevice, cCurrLinkGen)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCurrPcieLinkWidth function as declared in nvml/nvml.h
func nvmlDeviceGetCurrPcieLinkWidth(Device Device, CurrLinkWidth *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrLinkWidth, _ := (*C.uint)(unsafe.Pointer(CurrLinkWidth)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCurrPcieLinkWidth(cDevice, cCurrLinkWidth)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPcieThroughput function as declared in nvml/nvml.h
func nvmlDeviceGetPcieThroughput(Device Device, Counter PcieUtilCounter, Value *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCounter, _ := (C.nvmlPcieUtilCounter_t)(Counter), cgoAllocsUnknown
	cValue, _ := (*C.uint)(unsafe.Pointer(Value)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPcieThroughput(cDevice, cCounter, cValue)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPcieReplayCounter function as declared in nvml/nvml.h
func nvmlDeviceGetPcieReplayCounter(Device Device, Value *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cValue, _ := (*C.uint)(unsafe.Pointer(Value)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPcieReplayCounter(cDevice, cValue)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetClockInfo function as declared in nvml/nvml.h
func nvmlDeviceGetClockInfo(Device Device, _type ClockType, Clock *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	c_type, _ := (C.nvmlClockType_t)(_type), cgoAllocsUnknown
	cClock, _ := (*C.uint)(unsafe.Pointer(Clock)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetClockInfo(cDevice, c_type, cClock)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMaxClockInfo function as declared in nvml/nvml.h
func nvmlDeviceGetMaxClockInfo(Device Device, _type ClockType, Clock *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	c_type, _ := (C.nvmlClockType_t)(_type), cgoAllocsUnknown
	cClock, _ := (*C.uint)(unsafe.Pointer(Clock)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMaxClockInfo(cDevice, c_type, cClock)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetApplicationsClock function as declared in nvml/nvml.h
func nvmlDeviceGetApplicationsClock(Device Device, ClockType ClockType, ClockMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cClockType, _ := (C.nvmlClockType_t)(ClockType), cgoAllocsUnknown
	cClockMHz, _ := (*C.uint)(unsafe.Pointer(ClockMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetApplicationsClock(cDevice, cClockType, cClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDefaultApplicationsClock function as declared in nvml/nvml.h
func nvmlDeviceGetDefaultApplicationsClock(Device Device, ClockType ClockType, ClockMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cClockType, _ := (C.nvmlClockType_t)(ClockType), cgoAllocsUnknown
	cClockMHz, _ := (*C.uint)(unsafe.Pointer(ClockMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDefaultApplicationsClock(cDevice, cClockType, cClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceResetApplicationsClocks function as declared in nvml/nvml.h
func nvmlDeviceResetApplicationsClocks(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceResetApplicationsClocks(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetClock function as declared in nvml/nvml.h
func nvmlDeviceGetClock(Device Device, ClockType ClockType, ClockId ClockId, ClockMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cClockType, _ := (C.nvmlClockType_t)(ClockType), cgoAllocsUnknown
	cClockId, _ := (C.nvmlClockId_t)(ClockId), cgoAllocsUnknown
	cClockMHz, _ := (*C.uint)(unsafe.Pointer(ClockMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetClock(cDevice, cClockType, cClockId, cClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMaxCustomerBoostClock function as declared in nvml/nvml.h
func nvmlDeviceGetMaxCustomerBoostClock(Device Device, ClockType ClockType, ClockMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cClockType, _ := (C.nvmlClockType_t)(ClockType), cgoAllocsUnknown
	cClockMHz, _ := (*C.uint)(unsafe.Pointer(ClockMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMaxCustomerBoostClock(cDevice, cClockType, cClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSupportedMemoryClocks function as declared in nvml/nvml.h
func nvmlDeviceGetSupportedMemoryClocks(Device Device, Count *uint32, ClocksMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cClocksMHz, _ := (*C.uint)(unsafe.Pointer(ClocksMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSupportedMemoryClocks(cDevice, cCount, cClocksMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSupportedGraphicsClocks function as declared in nvml/nvml.h
func nvmlDeviceGetSupportedGraphicsClocks(Device Device, MemoryClockMHz uint32, Count *uint32, ClocksMHz *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMemoryClockMHz, _ := (C.uint)(MemoryClockMHz), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cClocksMHz, _ := (*C.uint)(unsafe.Pointer(ClocksMHz)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSupportedGraphicsClocks(cDevice, cMemoryClockMHz, cCount, cClocksMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAutoBoostedClocksEnabled function as declared in nvml/nvml.h
func nvmlDeviceGetAutoBoostedClocksEnabled(Device Device, IsEnabled *EnableState, DefaultIsEnabled *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIsEnabled, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(IsEnabled)), cgoAllocsUnknown
	cDefaultIsEnabled, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(DefaultIsEnabled)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAutoBoostedClocksEnabled(cDevice, cIsEnabled, cDefaultIsEnabled)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetAutoBoostedClocksEnabled function as declared in nvml/nvml.h
func nvmlDeviceSetAutoBoostedClocksEnabled(Device Device, Enabled EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEnabled, _ := (C.nvmlEnableState_t)(Enabled), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetAutoBoostedClocksEnabled(cDevice, cEnabled)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetDefaultAutoBoostedClocksEnabled function as declared in nvml/nvml.h
func nvmlDeviceSetDefaultAutoBoostedClocksEnabled(Device Device, Enabled EnableState, Flags uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEnabled, _ := (C.nvmlEnableState_t)(Enabled), cgoAllocsUnknown
	cFlags, _ := (C.uint)(Flags), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetDefaultAutoBoostedClocksEnabled(cDevice, cEnabled, cFlags)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetFanSpeed function as declared in nvml/nvml.h
func nvmlDeviceGetFanSpeed(Device Device, Speed *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSpeed, _ := (*C.uint)(unsafe.Pointer(Speed)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetFanSpeed(cDevice, cSpeed)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetFanSpeed_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetFanSpeed_v2(Device Device, Fan uint32, Speed *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cFan, _ := (C.uint)(Fan), cgoAllocsUnknown
	cSpeed, _ := (*C.uint)(unsafe.Pointer(Speed)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetFanSpeed_v2(cDevice, cFan, cSpeed)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNumFans function as declared in nvml/nvml.h
func nvmlDeviceGetNumFans(Device Device, NumFans *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cNumFans, _ := (*C.uint)(unsafe.Pointer(NumFans)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNumFans(cDevice, cNumFans)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTemperature function as declared in nvml/nvml.h
func nvmlDeviceGetTemperature(Device Device, SensorType TemperatureSensors, Temp *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSensorType, _ := (C.nvmlTemperatureSensors_t)(SensorType), cgoAllocsUnknown
	cTemp, _ := (*C.uint)(unsafe.Pointer(Temp)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTemperature(cDevice, cSensorType, cTemp)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTemperatureThreshold function as declared in nvml/nvml.h
func nvmlDeviceGetTemperatureThreshold(Device Device, ThresholdType TemperatureThresholds, Temp *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cThresholdType, _ := (C.nvmlTemperatureThresholds_t)(ThresholdType), cgoAllocsUnknown
	cTemp, _ := (*C.uint)(unsafe.Pointer(Temp)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTemperatureThreshold(cDevice, cThresholdType, cTemp)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetTemperatureThreshold function as declared in nvml/nvml.h
func nvmlDeviceSetTemperatureThreshold(Device Device, ThresholdType TemperatureThresholds, Temp *int32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cThresholdType, _ := (C.nvmlTemperatureThresholds_t)(ThresholdType), cgoAllocsUnknown
	cTemp, _ := (*C.int)(unsafe.Pointer(Temp)), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetTemperatureThreshold(cDevice, cThresholdType, cTemp)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPerformanceState function as declared in nvml/nvml.h
func nvmlDeviceGetPerformanceState(Device Device, PState *Pstates) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPState, _ := (*C.nvmlPstates_t)(unsafe.Pointer(PState)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPerformanceState(cDevice, cPState)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCurrentClocksThrottleReasons function as declared in nvml/nvml.h
func nvmlDeviceGetCurrentClocksThrottleReasons(Device Device, ClocksThrottleReasons *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cClocksThrottleReasons, _ := (*C.ulonglong)(unsafe.Pointer(ClocksThrottleReasons)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCurrentClocksThrottleReasons(cDevice, cClocksThrottleReasons)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSupportedClocksThrottleReasons function as declared in nvml/nvml.h
func nvmlDeviceGetSupportedClocksThrottleReasons(Device Device, SupportedClocksThrottleReasons *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSupportedClocksThrottleReasons, _ := (*C.ulonglong)(unsafe.Pointer(SupportedClocksThrottleReasons)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSupportedClocksThrottleReasons(cDevice, cSupportedClocksThrottleReasons)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerState function as declared in nvml/nvml.h
func nvmlDeviceGetPowerState(Device Device, PState *Pstates) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPState, _ := (*C.nvmlPstates_t)(unsafe.Pointer(PState)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerState(cDevice, cPState)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerManagementMode function as declared in nvml/nvml.h
func nvmlDeviceGetPowerManagementMode(Device Device, Mode *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Mode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerManagementMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerManagementLimit function as declared in nvml/nvml.h
func nvmlDeviceGetPowerManagementLimit(Device Device, Limit *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLimit, _ := (*C.uint)(unsafe.Pointer(Limit)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerManagementLimit(cDevice, cLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerManagementLimitConstraints function as declared in nvml/nvml.h
func nvmlDeviceGetPowerManagementLimitConstraints(Device Device, MinLimit *uint32, MaxLimit *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMinLimit, _ := (*C.uint)(unsafe.Pointer(MinLimit)), cgoAllocsUnknown
	cMaxLimit, _ := (*C.uint)(unsafe.Pointer(MaxLimit)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerManagementLimitConstraints(cDevice, cMinLimit, cMaxLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerManagementDefaultLimit function as declared in nvml/nvml.h
func nvmlDeviceGetPowerManagementDefaultLimit(Device Device, DefaultLimit *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cDefaultLimit, _ := (*C.uint)(unsafe.Pointer(DefaultLimit)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerManagementDefaultLimit(cDevice, cDefaultLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerUsage function as declared in nvml/nvml.h
func nvmlDeviceGetPowerUsage(Device Device, Power *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPower, _ := (*C.uint)(unsafe.Pointer(Power)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerUsage(cDevice, cPower)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTotalEnergyConsumption function as declared in nvml/nvml.h
func nvmlDeviceGetTotalEnergyConsumption(Device Device, Energy *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEnergy, _ := (*C.ulonglong)(unsafe.Pointer(Energy)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTotalEnergyConsumption(cDevice, cEnergy)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEnforcedPowerLimit function as declared in nvml/nvml.h
func nvmlDeviceGetEnforcedPowerLimit(Device Device, Limit *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLimit, _ := (*C.uint)(unsafe.Pointer(Limit)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEnforcedPowerLimit(cDevice, cLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuOperationMode function as declared in nvml/nvml.h
func nvmlDeviceGetGpuOperationMode(Device Device, Current *GpuOperationMode, Pending *GpuOperationMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrent, _ := (*C.nvmlGpuOperationMode_t)(unsafe.Pointer(Current)), cgoAllocsUnknown
	cPending, _ := (*C.nvmlGpuOperationMode_t)(unsafe.Pointer(Pending)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuOperationMode(cDevice, cCurrent, cPending)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMemoryInfo function as declared in nvml/nvml.h
func nvmlDeviceGetMemoryInfo(Device Device, Memory *Memory) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMemory, _ := (*C.nvmlMemory_t)(unsafe.Pointer(Memory)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMemoryInfo(cDevice, cMemory)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMemoryInfo_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetMemoryInfo_v2(Device Device, Memory *Memory_v2) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMemory, _ := (*C.nvmlMemory_v2_t)(unsafe.Pointer(Memory)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMemoryInfo_v2(cDevice, cMemory)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetComputeMode function as declared in nvml/nvml.h
func nvmlDeviceGetComputeMode(Device Device, Mode *ComputeMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (*C.nvmlComputeMode_t)(unsafe.Pointer(Mode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetComputeMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCudaComputeCapability function as declared in nvml/nvml.h
func nvmlDeviceGetCudaComputeCapability(Device Device, Major *int32, Minor *int32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMajor, _ := (*C.int)(unsafe.Pointer(Major)), cgoAllocsUnknown
	cMinor, _ := (*C.int)(unsafe.Pointer(Minor)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCudaComputeCapability(cDevice, cMajor, cMinor)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEccMode function as declared in nvml/nvml.h
func nvmlDeviceGetEccMode(Device Device, Current *EnableState, Pending *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrent, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Current)), cgoAllocsUnknown
	cPending, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Pending)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEccMode(cDevice, cCurrent, cPending)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBoardId function as declared in nvml/nvml.h
func nvmlDeviceGetBoardId(Device Device, BoardId *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cBoardId, _ := (*C.uint)(unsafe.Pointer(BoardId)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBoardId(cDevice, cBoardId)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMultiGpuBoard function as declared in nvml/nvml.h
func nvmlDeviceGetMultiGpuBoard(Device Device, MultiGpuBool *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMultiGpuBool, _ := (*C.uint)(unsafe.Pointer(MultiGpuBool)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMultiGpuBoard(cDevice, cMultiGpuBool)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetTotalEccErrors function as declared in nvml/nvml.h
func nvmlDeviceGetTotalEccErrors(Device Device, ErrorType MemoryErrorType, CounterType EccCounterType, EccCounts *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cErrorType, _ := (C.nvmlMemoryErrorType_t)(ErrorType), cgoAllocsUnknown
	cCounterType, _ := (C.nvmlEccCounterType_t)(CounterType), cgoAllocsUnknown
	cEccCounts, _ := (*C.ulonglong)(unsafe.Pointer(EccCounts)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetTotalEccErrors(cDevice, cErrorType, cCounterType, cEccCounts)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDetailedEccErrors function as declared in nvml/nvml.h
func nvmlDeviceGetDetailedEccErrors(Device Device, ErrorType MemoryErrorType, CounterType EccCounterType, EccCounts *EccErrorCounts) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cErrorType, _ := (C.nvmlMemoryErrorType_t)(ErrorType), cgoAllocsUnknown
	cCounterType, _ := (C.nvmlEccCounterType_t)(CounterType), cgoAllocsUnknown
	cEccCounts, _ := (*C.nvmlEccErrorCounts_t)(unsafe.Pointer(EccCounts)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDetailedEccErrors(cDevice, cErrorType, cCounterType, cEccCounts)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMemoryErrorCounter function as declared in nvml/nvml.h
func nvmlDeviceGetMemoryErrorCounter(Device Device, ErrorType MemoryErrorType, CounterType EccCounterType, LocationType MemoryLocation, Count *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cErrorType, _ := (C.nvmlMemoryErrorType_t)(ErrorType), cgoAllocsUnknown
	cCounterType, _ := (C.nvmlEccCounterType_t)(CounterType), cgoAllocsUnknown
	cLocationType, _ := (C.nvmlMemoryLocation_t)(LocationType), cgoAllocsUnknown
	cCount, _ := (*C.ulonglong)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMemoryErrorCounter(cDevice, cErrorType, cCounterType, cLocationType, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetUtilizationRates function as declared in nvml/nvml.h
func nvmlDeviceGetUtilizationRates(Device Device, Utilization *Utilization) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cUtilization, _ := (*C.nvmlUtilization_t)(unsafe.Pointer(Utilization)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetUtilizationRates(cDevice, cUtilization)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEncoderUtilization function as declared in nvml/nvml.h
func nvmlDeviceGetEncoderUtilization(Device Device, Utilization *uint32, SamplingPeriodUs *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cUtilization, _ := (*C.uint)(unsafe.Pointer(Utilization)), cgoAllocsUnknown
	cSamplingPeriodUs, _ := (*C.uint)(unsafe.Pointer(SamplingPeriodUs)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEncoderUtilization(cDevice, cUtilization, cSamplingPeriodUs)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEncoderCapacity function as declared in nvml/nvml.h
func nvmlDeviceGetEncoderCapacity(Device Device, EncoderQueryType EncoderType, EncoderCapacity *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEncoderQueryType, _ := (C.nvmlEncoderType_t)(EncoderQueryType), cgoAllocsUnknown
	cEncoderCapacity, _ := (*C.uint)(unsafe.Pointer(EncoderCapacity)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEncoderCapacity(cDevice, cEncoderQueryType, cEncoderCapacity)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEncoderStats function as declared in nvml/nvml.h
func nvmlDeviceGetEncoderStats(Device Device, SessionCount *uint32, AverageFps *uint32, AverageLatency *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cAverageFps, _ := (*C.uint)(unsafe.Pointer(AverageFps)), cgoAllocsUnknown
	cAverageLatency, _ := (*C.uint)(unsafe.Pointer(AverageLatency)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEncoderStats(cDevice, cSessionCount, cAverageFps, cAverageLatency)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetEncoderSessions function as declared in nvml/nvml.h
func nvmlDeviceGetEncoderSessions(Device Device, SessionCount *uint32, SessionInfos *EncoderSessionInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cSessionInfos, _ := (*C.nvmlEncoderSessionInfo_t)(unsafe.Pointer(SessionInfos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetEncoderSessions(cDevice, cSessionCount, cSessionInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDecoderUtilization function as declared in nvml/nvml.h
func nvmlDeviceGetDecoderUtilization(Device Device, Utilization *uint32, SamplingPeriodUs *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cUtilization, _ := (*C.uint)(unsafe.Pointer(Utilization)), cgoAllocsUnknown
	cSamplingPeriodUs, _ := (*C.uint)(unsafe.Pointer(SamplingPeriodUs)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDecoderUtilization(cDevice, cUtilization, cSamplingPeriodUs)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetFBCStats function as declared in nvml/nvml.h
func nvmlDeviceGetFBCStats(Device Device, FbcStats *FBCStats) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cFbcStats, _ := (*C.nvmlFBCStats_t)(unsafe.Pointer(FbcStats)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetFBCStats(cDevice, cFbcStats)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetFBCSessions function as declared in nvml/nvml.h
func nvmlDeviceGetFBCSessions(Device Device, SessionCount *uint32, SessionInfo *FBCSessionInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cSessionInfo, _ := (*C.nvmlFBCSessionInfo_t)(unsafe.Pointer(SessionInfo)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetFBCSessions(cDevice, cSessionCount, cSessionInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDriverModel function as declared in nvml/nvml.h
func nvmlDeviceGetDriverModel(Device Device, Current *DriverModel, Pending *DriverModel) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrent, _ := (*C.nvmlDriverModel_t)(unsafe.Pointer(Current)), cgoAllocsUnknown
	cPending, _ := (*C.nvmlDriverModel_t)(unsafe.Pointer(Pending)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDriverModel(cDevice, cCurrent, cPending)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetVbiosVersion function as declared in nvml/nvml.h
func nvmlDeviceGetVbiosVersion(Device Device, Version *byte, Length uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetVbiosVersion(cDevice, cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBridgeChipInfo function as declared in nvml/nvml.h
func nvmlDeviceGetBridgeChipInfo(Device Device, BridgeHierarchy *BridgeChipHierarchy) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cBridgeHierarchy, _ := (*C.nvmlBridgeChipHierarchy_t)(unsafe.Pointer(BridgeHierarchy)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBridgeChipInfo(cDevice, cBridgeHierarchy)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetComputeRunningProcesses_v3 function as declared in nvml/nvml.h
func nvmlDeviceGetComputeRunningProcesses_v3(Device Device, InfoCount *uint32, Infos *ProcessInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetComputeRunningProcesses_v3(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGraphicsRunningProcesses_v3 function as declared in nvml/nvml.h
func nvmlDeviceGetGraphicsRunningProcesses_v3(Device Device, InfoCount *uint32, Infos *ProcessInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGraphicsRunningProcesses_v3(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMPSComputeRunningProcesses_v3 function as declared in nvml/nvml.h
func nvmlDeviceGetMPSComputeRunningProcesses_v3(Device Device, InfoCount *uint32, Infos *ProcessInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMPSComputeRunningProcesses_v3(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceOnSameBoard function as declared in nvml/nvml.h
func nvmlDeviceOnSameBoard(Device1 Device, Device2 Device, OnSameBoard *int32) Return {
	cDevice1, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device1)), cgoAllocsUnknown
	cDevice2, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device2)), cgoAllocsUnknown
	cOnSameBoard, _ := (*C.int)(unsafe.Pointer(OnSameBoard)), cgoAllocsUnknown
	__ret := C.nvmlDeviceOnSameBoard(cDevice1, cDevice2, cOnSameBoard)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAPIRestriction function as declared in nvml/nvml.h
func nvmlDeviceGetAPIRestriction(Device Device, ApiType RestrictedAPI, IsRestricted *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cApiType, _ := (C.nvmlRestrictedAPI_t)(ApiType), cgoAllocsUnknown
	cIsRestricted, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(IsRestricted)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAPIRestriction(cDevice, cApiType, cIsRestricted)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSamples function as declared in nvml/nvml.h
func nvmlDeviceGetSamples(Device Device, _type SamplingType, LastSeenTimeStamp uint64, SampleValType *ValueType, SampleCount *uint32, Samples *Sample) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	c_type, _ := (C.nvmlSamplingType_t)(_type), cgoAllocsUnknown
	cLastSeenTimeStamp, _ := (C.ulonglong)(LastSeenTimeStamp), cgoAllocsUnknown
	cSampleValType, _ := (*C.nvmlValueType_t)(unsafe.Pointer(SampleValType)), cgoAllocsUnknown
	cSampleCount, _ := (*C.uint)(unsafe.Pointer(SampleCount)), cgoAllocsUnknown
	cSamples, _ := (*C.nvmlSample_t)(unsafe.Pointer(Samples)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSamples(cDevice, c_type, cLastSeenTimeStamp, cSampleValType, cSampleCount, cSamples)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBAR1MemoryInfo function as declared in nvml/nvml.h
func nvmlDeviceGetBAR1MemoryInfo(Device Device, Bar1Memory *BAR1Memory) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cBar1Memory, _ := (*C.nvmlBAR1Memory_t)(unsafe.Pointer(Bar1Memory)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBAR1MemoryInfo(cDevice, cBar1Memory)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetViolationStatus function as declared in nvml/nvml.h
func nvmlDeviceGetViolationStatus(Device Device, PerfPolicyType PerfPolicyType, ViolTime *ViolationTime) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPerfPolicyType, _ := (C.nvmlPerfPolicyType_t)(PerfPolicyType), cgoAllocsUnknown
	cViolTime, _ := (*C.nvmlViolationTime_t)(unsafe.Pointer(ViolTime)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetViolationStatus(cDevice, cPerfPolicyType, cViolTime)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetIrqNum function as declared in nvml/nvml.h
func nvmlDeviceGetIrqNum(Device Device, IrqNum *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIrqNum, _ := (*C.uint)(unsafe.Pointer(IrqNum)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetIrqNum(cDevice, cIrqNum)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNumGpuCores function as declared in nvml/nvml.h
func nvmlDeviceGetNumGpuCores(Device Device, NumCores *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cNumCores, _ := (*C.uint)(unsafe.Pointer(NumCores)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNumGpuCores(cDevice, cNumCores)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPowerSource function as declared in nvml/nvml.h
func nvmlDeviceGetPowerSource(Device Device, PowerSource *PowerSource) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPowerSource, _ := (*C.nvmlPowerSource_t)(unsafe.Pointer(PowerSource)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPowerSource(cDevice, cPowerSource)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMemoryBusWidth function as declared in nvml/nvml.h
func nvmlDeviceGetMemoryBusWidth(Device Device, BusWidth *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cBusWidth, _ := (*C.uint)(unsafe.Pointer(BusWidth)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMemoryBusWidth(cDevice, cBusWidth)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPcieLinkMaxSpeed function as declared in nvml/nvml.h
func nvmlDeviceGetPcieLinkMaxSpeed(Device Device, MaxSpeed *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMaxSpeed, _ := (*C.uint)(unsafe.Pointer(MaxSpeed)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPcieLinkMaxSpeed(cDevice, cMaxSpeed)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAdaptiveClockInfoStatus function as declared in nvml/nvml.h
func nvmlDeviceGetAdaptiveClockInfoStatus(Device Device, AdaptiveClockStatus *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cAdaptiveClockStatus, _ := (*C.uint)(unsafe.Pointer(AdaptiveClockStatus)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAdaptiveClockInfoStatus(cDevice, cAdaptiveClockStatus)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAccountingMode function as declared in nvml/nvml.h
func nvmlDeviceGetAccountingMode(Device Device, Mode *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Mode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAccountingMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAccountingStats function as declared in nvml/nvml.h
func nvmlDeviceGetAccountingStats(Device Device, Pid uint32, Stats *AccountingStats) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPid, _ := (C.uint)(Pid), cgoAllocsUnknown
	cStats, _ := (*C.nvmlAccountingStats_t)(unsafe.Pointer(Stats)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAccountingStats(cDevice, cPid, cStats)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAccountingPids function as declared in nvml/nvml.h
func nvmlDeviceGetAccountingPids(Device Device, Count *uint32, Pids *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cPids, _ := (*C.uint)(unsafe.Pointer(Pids)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAccountingPids(cDevice, cCount, cPids)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAccountingBufferSize function as declared in nvml/nvml.h
func nvmlDeviceGetAccountingBufferSize(Device Device, BufferSize *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cBufferSize, _ := (*C.uint)(unsafe.Pointer(BufferSize)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAccountingBufferSize(cDevice, cBufferSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetRetiredPages function as declared in nvml/nvml.h
func nvmlDeviceGetRetiredPages(Device Device, Cause PageRetirementCause, PageCount *uint32, Addresses *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCause, _ := (C.nvmlPageRetirementCause_t)(Cause), cgoAllocsUnknown
	cPageCount, _ := (*C.uint)(unsafe.Pointer(PageCount)), cgoAllocsUnknown
	cAddresses, _ := (*C.ulonglong)(unsafe.Pointer(Addresses)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetRetiredPages(cDevice, cCause, cPageCount, cAddresses)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetRetiredPages_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetRetiredPages_v2(Device Device, Cause PageRetirementCause, PageCount *uint32, Addresses *uint64, Timestamps *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCause, _ := (C.nvmlPageRetirementCause_t)(Cause), cgoAllocsUnknown
	cPageCount, _ := (*C.uint)(unsafe.Pointer(PageCount)), cgoAllocsUnknown
	cAddresses, _ := (*C.ulonglong)(unsafe.Pointer(Addresses)), cgoAllocsUnknown
	cTimestamps, _ := (*C.ulonglong)(unsafe.Pointer(Timestamps)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetRetiredPages_v2(cDevice, cCause, cPageCount, cAddresses, cTimestamps)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetRetiredPagesPendingStatus function as declared in nvml/nvml.h
func nvmlDeviceGetRetiredPagesPendingStatus(Device Device, IsPending *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIsPending, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(IsPending)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetRetiredPagesPendingStatus(cDevice, cIsPending)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetRemappedRows function as declared in nvml/nvml.h
func nvmlDeviceGetRemappedRows(Device Device, CorrRows *uint32, UncRows *uint32, IsPending *uint32, FailureOccurred *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCorrRows, _ := (*C.uint)(unsafe.Pointer(CorrRows)), cgoAllocsUnknown
	cUncRows, _ := (*C.uint)(unsafe.Pointer(UncRows)), cgoAllocsUnknown
	cIsPending, _ := (*C.uint)(unsafe.Pointer(IsPending)), cgoAllocsUnknown
	cFailureOccurred, _ := (*C.uint)(unsafe.Pointer(FailureOccurred)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetRemappedRows(cDevice, cCorrRows, cUncRows, cIsPending, cFailureOccurred)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetRowRemapperHistogram function as declared in nvml/nvml.h
func nvmlDeviceGetRowRemapperHistogram(Device Device, Values *RowRemapperHistogramValues) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cValues, _ := (*C.nvmlRowRemapperHistogramValues_t)(unsafe.Pointer(Values)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetRowRemapperHistogram(cDevice, cValues)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetArchitecture function as declared in nvml/nvml.h
func nvmlDeviceGetArchitecture(Device Device, Arch *DeviceArchitecture) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cArch, _ := (*C.nvmlDeviceArchitecture_t)(unsafe.Pointer(Arch)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetArchitecture(cDevice, cArch)
	__v := (Return)(__ret)
	return __v
}

// nvmlUnitSetLedState function as declared in nvml/nvml.h
func nvmlUnitSetLedState(Unit Unit, Color LedColor) Return {
	cUnit, _ := *(*C.nvmlUnit_t)(unsafe.Pointer(&Unit)), cgoAllocsUnknown
	cColor, _ := (C.nvmlLedColor_t)(Color), cgoAllocsUnknown
	__ret := C.nvmlUnitSetLedState(cUnit, cColor)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetPersistenceMode function as declared in nvml/nvml.h
func nvmlDeviceSetPersistenceMode(Device Device, Mode EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (C.nvmlEnableState_t)(Mode), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetPersistenceMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetComputeMode function as declared in nvml/nvml.h
func nvmlDeviceSetComputeMode(Device Device, Mode ComputeMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (C.nvmlComputeMode_t)(Mode), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetComputeMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetEccMode function as declared in nvml/nvml.h
func nvmlDeviceSetEccMode(Device Device, Ecc EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEcc, _ := (C.nvmlEnableState_t)(Ecc), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetEccMode(cDevice, cEcc)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceClearEccErrorCounts function as declared in nvml/nvml.h
func nvmlDeviceClearEccErrorCounts(Device Device, CounterType EccCounterType) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCounterType, _ := (C.nvmlEccCounterType_t)(CounterType), cgoAllocsUnknown
	__ret := C.nvmlDeviceClearEccErrorCounts(cDevice, cCounterType)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetDriverModel function as declared in nvml/nvml.h
func nvmlDeviceSetDriverModel(Device Device, DriverModel DriverModel, Flags uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cDriverModel, _ := (C.nvmlDriverModel_t)(DriverModel), cgoAllocsUnknown
	cFlags, _ := (C.uint)(Flags), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetDriverModel(cDevice, cDriverModel, cFlags)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetGpuLockedClocks function as declared in nvml/nvml.h
func nvmlDeviceSetGpuLockedClocks(Device Device, MinGpuClockMHz uint32, MaxGpuClockMHz uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMinGpuClockMHz, _ := (C.uint)(MinGpuClockMHz), cgoAllocsUnknown
	cMaxGpuClockMHz, _ := (C.uint)(MaxGpuClockMHz), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetGpuLockedClocks(cDevice, cMinGpuClockMHz, cMaxGpuClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceResetGpuLockedClocks function as declared in nvml/nvml.h
func nvmlDeviceResetGpuLockedClocks(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceResetGpuLockedClocks(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetMemoryLockedClocks function as declared in nvml/nvml.h
func nvmlDeviceSetMemoryLockedClocks(Device Device, MinMemClockMHz uint32, MaxMemClockMHz uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMinMemClockMHz, _ := (C.uint)(MinMemClockMHz), cgoAllocsUnknown
	cMaxMemClockMHz, _ := (C.uint)(MaxMemClockMHz), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetMemoryLockedClocks(cDevice, cMinMemClockMHz, cMaxMemClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceResetMemoryLockedClocks function as declared in nvml/nvml.h
func nvmlDeviceResetMemoryLockedClocks(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceResetMemoryLockedClocks(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetApplicationsClocks function as declared in nvml/nvml.h
func nvmlDeviceSetApplicationsClocks(Device Device, MemClockMHz uint32, GraphicsClockMHz uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMemClockMHz, _ := (C.uint)(MemClockMHz), cgoAllocsUnknown
	cGraphicsClockMHz, _ := (C.uint)(GraphicsClockMHz), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetApplicationsClocks(cDevice, cMemClockMHz, cGraphicsClockMHz)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetClkMonStatus function as declared in nvml/nvml.h
func nvmlDeviceGetClkMonStatus(Device Device, Status *ClkMonStatus) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cStatus, _ := (*C.nvmlClkMonStatus_t)(unsafe.Pointer(Status)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetClkMonStatus(cDevice, cStatus)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetPowerManagementLimit function as declared in nvml/nvml.h
func nvmlDeviceSetPowerManagementLimit(Device Device, Limit uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLimit, _ := (C.uint)(Limit), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetPowerManagementLimit(cDevice, cLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetGpuOperationMode function as declared in nvml/nvml.h
func nvmlDeviceSetGpuOperationMode(Device Device, Mode GpuOperationMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (C.nvmlGpuOperationMode_t)(Mode), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetGpuOperationMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetAPIRestriction function as declared in nvml/nvml.h
func nvmlDeviceSetAPIRestriction(Device Device, ApiType RestrictedAPI, IsRestricted EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cApiType, _ := (C.nvmlRestrictedAPI_t)(ApiType), cgoAllocsUnknown
	cIsRestricted, _ := (C.nvmlEnableState_t)(IsRestricted), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetAPIRestriction(cDevice, cApiType, cIsRestricted)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetAccountingMode function as declared in nvml/nvml.h
func nvmlDeviceSetAccountingMode(Device Device, Mode EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (C.nvmlEnableState_t)(Mode), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetAccountingMode(cDevice, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceClearAccountingPids function as declared in nvml/nvml.h
func nvmlDeviceClearAccountingPids(Device Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceClearAccountingPids(cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkState function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkState(Device Device, Link uint32, IsActive *EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cIsActive, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(IsActive)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkState(cDevice, cLink, cIsActive)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkVersion function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkVersion(Device Device, Link uint32, Version *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cVersion, _ := (*C.uint)(unsafe.Pointer(Version)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkVersion(cDevice, cLink, cVersion)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkCapability function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkCapability(Device Device, Link uint32, Capability NvLinkCapability, CapResult *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCapability, _ := (C.nvmlNvLinkCapability_t)(Capability), cgoAllocsUnknown
	cCapResult, _ := (*C.uint)(unsafe.Pointer(CapResult)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkCapability(cDevice, cLink, cCapability, cCapResult)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkRemotePciInfo_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkRemotePciInfo_v2(Device Device, Link uint32, Pci *PciInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cPci, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(Pci)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkRemotePciInfo_v2(cDevice, cLink, cPci)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkErrorCounter function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkErrorCounter(Device Device, Link uint32, Counter NvLinkErrorCounter, CounterValue *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.nvmlNvLinkErrorCounter_t)(Counter), cgoAllocsUnknown
	cCounterValue, _ := (*C.ulonglong)(unsafe.Pointer(CounterValue)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkErrorCounter(cDevice, cLink, cCounter, cCounterValue)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceResetNvLinkErrorCounters function as declared in nvml/nvml.h
func nvmlDeviceResetNvLinkErrorCounters(Device Device, Link uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	__ret := C.nvmlDeviceResetNvLinkErrorCounters(cDevice, cLink)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetNvLinkUtilizationControl function as declared in nvml/nvml.h
func nvmlDeviceSetNvLinkUtilizationControl(Device Device, Link uint32, Counter uint32, Control *NvLinkUtilizationControl, Reset uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.uint)(Counter), cgoAllocsUnknown
	cControl, _ := (*C.nvmlNvLinkUtilizationControl_t)(unsafe.Pointer(Control)), cgoAllocsUnknown
	cReset, _ := (C.uint)(Reset), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetNvLinkUtilizationControl(cDevice, cLink, cCounter, cControl, cReset)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkUtilizationControl function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkUtilizationControl(Device Device, Link uint32, Counter uint32, Control *NvLinkUtilizationControl) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.uint)(Counter), cgoAllocsUnknown
	cControl, _ := (*C.nvmlNvLinkUtilizationControl_t)(unsafe.Pointer(Control)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkUtilizationControl(cDevice, cLink, cCounter, cControl)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkUtilizationCounter function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkUtilizationCounter(Device Device, Link uint32, Counter uint32, Rxcounter *uint64, Txcounter *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.uint)(Counter), cgoAllocsUnknown
	cRxcounter, _ := (*C.ulonglong)(unsafe.Pointer(Rxcounter)), cgoAllocsUnknown
	cTxcounter, _ := (*C.ulonglong)(unsafe.Pointer(Txcounter)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkUtilizationCounter(cDevice, cLink, cCounter, cRxcounter, cTxcounter)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceFreezeNvLinkUtilizationCounter function as declared in nvml/nvml.h
func nvmlDeviceFreezeNvLinkUtilizationCounter(Device Device, Link uint32, Counter uint32, Freeze EnableState) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.uint)(Counter), cgoAllocsUnknown
	cFreeze, _ := (C.nvmlEnableState_t)(Freeze), cgoAllocsUnknown
	__ret := C.nvmlDeviceFreezeNvLinkUtilizationCounter(cDevice, cLink, cCounter, cFreeze)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceResetNvLinkUtilizationCounter function as declared in nvml/nvml.h
func nvmlDeviceResetNvLinkUtilizationCounter(Device Device, Link uint32, Counter uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cCounter, _ := (C.uint)(Counter), cgoAllocsUnknown
	__ret := C.nvmlDeviceResetNvLinkUtilizationCounter(cDevice, cLink, cCounter)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkRemoteDeviceType function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkRemoteDeviceType(Device Device, Link uint32, PNvLinkDeviceType *IntNvLinkDeviceType) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cPNvLinkDeviceType, _ := (*C.nvmlIntNvLinkDeviceType_t)(unsafe.Pointer(PNvLinkDeviceType)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkRemoteDeviceType(cDevice, cLink, cPNvLinkDeviceType)
	__v := (Return)(__ret)
	return __v
}

// nvmlEventSetCreate function as declared in nvml/nvml.h
func nvmlEventSetCreate(Set *EventSet) Return {
	cSet, _ := (*C.nvmlEventSet_t)(unsafe.Pointer(Set)), cgoAllocsUnknown
	__ret := C.nvmlEventSetCreate(cSet)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceRegisterEvents function as declared in nvml/nvml.h
func nvmlDeviceRegisterEvents(Device Device, EventTypes uint64, Set EventSet) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEventTypes, _ := (C.ulonglong)(EventTypes), cgoAllocsUnknown
	cSet, _ := *(*C.nvmlEventSet_t)(unsafe.Pointer(&Set)), cgoAllocsUnknown
	__ret := C.nvmlDeviceRegisterEvents(cDevice, cEventTypes, cSet)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSupportedEventTypes function as declared in nvml/nvml.h
func nvmlDeviceGetSupportedEventTypes(Device Device, EventTypes *uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cEventTypes, _ := (*C.ulonglong)(unsafe.Pointer(EventTypes)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSupportedEventTypes(cDevice, cEventTypes)
	__v := (Return)(__ret)
	return __v
}

// nvmlEventSetWait_v2 function as declared in nvml/nvml.h
func nvmlEventSetWait_v2(Set EventSet, Data *EventData, Timeoutms uint32) Return {
	cSet, _ := *(*C.nvmlEventSet_t)(unsafe.Pointer(&Set)), cgoAllocsUnknown
	cData, _ := (*C.nvmlEventData_t)(unsafe.Pointer(Data)), cgoAllocsUnknown
	cTimeoutms, _ := (C.uint)(Timeoutms), cgoAllocsUnknown
	__ret := C.nvmlEventSetWait_v2(cSet, cData, cTimeoutms)
	__v := (Return)(__ret)
	return __v
}

// nvmlEventSetFree function as declared in nvml/nvml.h
func nvmlEventSetFree(Set EventSet) Return {
	cSet, _ := *(*C.nvmlEventSet_t)(unsafe.Pointer(&Set)), cgoAllocsUnknown
	__ret := C.nvmlEventSetFree(cSet)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceModifyDrainState function as declared in nvml/nvml.h
func nvmlDeviceModifyDrainState(PciInfo *PciInfo, NewState EnableState) Return {
	cPciInfo, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(PciInfo)), cgoAllocsUnknown
	cNewState, _ := (C.nvmlEnableState_t)(NewState), cgoAllocsUnknown
	__ret := C.nvmlDeviceModifyDrainState(cPciInfo, cNewState)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceQueryDrainState function as declared in nvml/nvml.h
func nvmlDeviceQueryDrainState(PciInfo *PciInfo, CurrentState *EnableState) Return {
	cPciInfo, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(PciInfo)), cgoAllocsUnknown
	cCurrentState, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(CurrentState)), cgoAllocsUnknown
	__ret := C.nvmlDeviceQueryDrainState(cPciInfo, cCurrentState)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceRemoveGpu_v2 function as declared in nvml/nvml.h
func nvmlDeviceRemoveGpu_v2(PciInfo *PciInfo, GpuState DetachGpuState, LinkState PcieLinkState) Return {
	cPciInfo, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(PciInfo)), cgoAllocsUnknown
	cGpuState, _ := (C.nvmlDetachGpuState_t)(GpuState), cgoAllocsUnknown
	cLinkState, _ := (C.nvmlPcieLinkState_t)(LinkState), cgoAllocsUnknown
	__ret := C.nvmlDeviceRemoveGpu_v2(cPciInfo, cGpuState, cLinkState)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceDiscoverGpus function as declared in nvml/nvml.h
func nvmlDeviceDiscoverGpus(PciInfo *PciInfo) Return {
	cPciInfo, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(PciInfo)), cgoAllocsUnknown
	__ret := C.nvmlDeviceDiscoverGpus(cPciInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetFieldValues function as declared in nvml/nvml.h
func nvmlDeviceGetFieldValues(Device Device, ValuesCount int32, Values *FieldValue) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cValuesCount, _ := (C.int)(ValuesCount), cgoAllocsUnknown
	cValues, _ := (*C.nvmlFieldValue_t)(unsafe.Pointer(Values)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetFieldValues(cDevice, cValuesCount, cValues)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetVirtualizationMode function as declared in nvml/nvml.h
func nvmlDeviceGetVirtualizationMode(Device Device, PVirtualMode *GpuVirtualizationMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPVirtualMode, _ := (*C.nvmlGpuVirtualizationMode_t)(unsafe.Pointer(PVirtualMode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetVirtualizationMode(cDevice, cPVirtualMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHostVgpuMode function as declared in nvml/nvml.h
func nvmlDeviceGetHostVgpuMode(Device Device, PHostVgpuMode *HostVgpuMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPHostVgpuMode, _ := (*C.nvmlHostVgpuMode_t)(unsafe.Pointer(PHostVgpuMode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHostVgpuMode(cDevice, cPHostVgpuMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetVirtualizationMode function as declared in nvml/nvml.h
func nvmlDeviceSetVirtualizationMode(Device Device, VirtualMode GpuVirtualizationMode) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVirtualMode, _ := (C.nvmlGpuVirtualizationMode_t)(VirtualMode), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetVirtualizationMode(cDevice, cVirtualMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGridLicensableFeatures_v4 function as declared in nvml/nvml.h
func nvmlDeviceGetGridLicensableFeatures_v4(Device Device, PGridLicensableFeatures *GridLicensableFeatures) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPGridLicensableFeatures, _ := (*C.nvmlGridLicensableFeatures_t)(unsafe.Pointer(PGridLicensableFeatures)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGridLicensableFeatures_v4(cDevice, cPGridLicensableFeatures)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetProcessUtilization function as declared in nvml/nvml.h
func nvmlDeviceGetProcessUtilization(Device Device, Utilization *ProcessUtilizationSample, ProcessSamplesCount *uint32, LastSeenTimeStamp uint64) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cUtilization, _ := (*C.nvmlProcessUtilizationSample_t)(unsafe.Pointer(Utilization)), cgoAllocsUnknown
	cProcessSamplesCount, _ := (*C.uint)(unsafe.Pointer(ProcessSamplesCount)), cgoAllocsUnknown
	cLastSeenTimeStamp, _ := (C.ulonglong)(LastSeenTimeStamp), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetProcessUtilization(cDevice, cUtilization, cProcessSamplesCount, cLastSeenTimeStamp)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetSupportedVgpus function as declared in nvml/nvml.h
func nvmlDeviceGetSupportedVgpus(Device Device, VgpuCount *uint32, VgpuTypeIds *VgpuTypeId) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVgpuCount, _ := (*C.uint)(unsafe.Pointer(VgpuCount)), cgoAllocsUnknown
	cVgpuTypeIds, _ := (*C.nvmlVgpuTypeId_t)(unsafe.Pointer(VgpuTypeIds)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetSupportedVgpus(cDevice, cVgpuCount, cVgpuTypeIds)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCreatableVgpus function as declared in nvml/nvml.h
func nvmlDeviceGetCreatableVgpus(Device Device, VgpuCount *uint32, VgpuTypeIds *VgpuTypeId) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVgpuCount, _ := (*C.uint)(unsafe.Pointer(VgpuCount)), cgoAllocsUnknown
	cVgpuTypeIds, _ := (*C.nvmlVgpuTypeId_t)(unsafe.Pointer(VgpuTypeIds)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCreatableVgpus(cDevice, cVgpuCount, cVgpuTypeIds)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetClass function as declared in nvml/nvml.h
func nvmlVgpuTypeGetClass(VgpuTypeId VgpuTypeId, VgpuTypeClass *byte, Size *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cVgpuTypeClass, _ := (*C.char)(unsafe.Pointer(VgpuTypeClass)), cgoAllocsUnknown
	cSize, _ := (*C.uint)(unsafe.Pointer(Size)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetClass(cVgpuTypeId, cVgpuTypeClass, cSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetName function as declared in nvml/nvml.h
func nvmlVgpuTypeGetName(VgpuTypeId VgpuTypeId, VgpuTypeName *byte, Size *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cVgpuTypeName, _ := (*C.char)(unsafe.Pointer(VgpuTypeName)), cgoAllocsUnknown
	cSize, _ := (*C.uint)(unsafe.Pointer(Size)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetName(cVgpuTypeId, cVgpuTypeName, cSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetGpuInstanceProfileId function as declared in nvml/nvml.h
func nvmlVgpuTypeGetGpuInstanceProfileId(VgpuTypeId VgpuTypeId, GpuInstanceProfileId *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cGpuInstanceProfileId, _ := (*C.uint)(unsafe.Pointer(GpuInstanceProfileId)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetGpuInstanceProfileId(cVgpuTypeId, cGpuInstanceProfileId)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetDeviceID function as declared in nvml/nvml.h
func nvmlVgpuTypeGetDeviceID(VgpuTypeId VgpuTypeId, DeviceID *uint64, SubsystemID *uint64) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cDeviceID, _ := (*C.ulonglong)(unsafe.Pointer(DeviceID)), cgoAllocsUnknown
	cSubsystemID, _ := (*C.ulonglong)(unsafe.Pointer(SubsystemID)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetDeviceID(cVgpuTypeId, cDeviceID, cSubsystemID)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetFramebufferSize function as declared in nvml/nvml.h
func nvmlVgpuTypeGetFramebufferSize(VgpuTypeId VgpuTypeId, FbSize *uint64) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cFbSize, _ := (*C.ulonglong)(unsafe.Pointer(FbSize)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetFramebufferSize(cVgpuTypeId, cFbSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetNumDisplayHeads function as declared in nvml/nvml.h
func nvmlVgpuTypeGetNumDisplayHeads(VgpuTypeId VgpuTypeId, NumDisplayHeads *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cNumDisplayHeads, _ := (*C.uint)(unsafe.Pointer(NumDisplayHeads)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetNumDisplayHeads(cVgpuTypeId, cNumDisplayHeads)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetResolution function as declared in nvml/nvml.h
func nvmlVgpuTypeGetResolution(VgpuTypeId VgpuTypeId, DisplayIndex uint32, Xdim *uint32, Ydim *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cDisplayIndex, _ := (C.uint)(DisplayIndex), cgoAllocsUnknown
	cXdim, _ := (*C.uint)(unsafe.Pointer(Xdim)), cgoAllocsUnknown
	cYdim, _ := (*C.uint)(unsafe.Pointer(Ydim)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetResolution(cVgpuTypeId, cDisplayIndex, cXdim, cYdim)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetLicense function as declared in nvml/nvml.h
func nvmlVgpuTypeGetLicense(VgpuTypeId VgpuTypeId, VgpuTypeLicenseString *byte, Size uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cVgpuTypeLicenseString, _ := (*C.char)(unsafe.Pointer(VgpuTypeLicenseString)), cgoAllocsUnknown
	cSize, _ := (C.uint)(Size), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetLicense(cVgpuTypeId, cVgpuTypeLicenseString, cSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetFrameRateLimit function as declared in nvml/nvml.h
func nvmlVgpuTypeGetFrameRateLimit(VgpuTypeId VgpuTypeId, FrameRateLimit *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cFrameRateLimit, _ := (*C.uint)(unsafe.Pointer(FrameRateLimit)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetFrameRateLimit(cVgpuTypeId, cFrameRateLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetMaxInstances function as declared in nvml/nvml.h
func nvmlVgpuTypeGetMaxInstances(Device Device, VgpuTypeId VgpuTypeId, VgpuInstanceCount *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cVgpuInstanceCount, _ := (*C.uint)(unsafe.Pointer(VgpuInstanceCount)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetMaxInstances(cDevice, cVgpuTypeId, cVgpuInstanceCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuTypeGetMaxInstancesPerVm function as declared in nvml/nvml.h
func nvmlVgpuTypeGetMaxInstancesPerVm(VgpuTypeId VgpuTypeId, VgpuInstanceCountPerVm *uint32) Return {
	cVgpuTypeId, _ := (C.nvmlVgpuTypeId_t)(VgpuTypeId), cgoAllocsUnknown
	cVgpuInstanceCountPerVm, _ := (*C.uint)(unsafe.Pointer(VgpuInstanceCountPerVm)), cgoAllocsUnknown
	__ret := C.nvmlVgpuTypeGetMaxInstancesPerVm(cVgpuTypeId, cVgpuInstanceCountPerVm)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetActiveVgpus function as declared in nvml/nvml.h
func nvmlDeviceGetActiveVgpus(Device Device, VgpuCount *uint32, VgpuInstances *VgpuInstance) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cVgpuCount, _ := (*C.uint)(unsafe.Pointer(VgpuCount)), cgoAllocsUnknown
	cVgpuInstances, _ := (*C.nvmlVgpuInstance_t)(unsafe.Pointer(VgpuInstances)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetActiveVgpus(cDevice, cVgpuCount, cVgpuInstances)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetVmID function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetVmID(VgpuInstance VgpuInstance, VmId *byte, Size uint32, VmIdType *VgpuVmIdType) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cVmId, _ := (*C.char)(unsafe.Pointer(VmId)), cgoAllocsUnknown
	cSize, _ := (C.uint)(Size), cgoAllocsUnknown
	cVmIdType, _ := (*C.nvmlVgpuVmIdType_t)(unsafe.Pointer(VmIdType)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetVmID(cVgpuInstance, cVmId, cSize, cVmIdType)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetUUID function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetUUID(VgpuInstance VgpuInstance, Uuid *byte, Size uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cUuid, _ := (*C.char)(unsafe.Pointer(Uuid)), cgoAllocsUnknown
	cSize, _ := (C.uint)(Size), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetUUID(cVgpuInstance, cUuid, cSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetVmDriverVersion function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetVmDriverVersion(VgpuInstance VgpuInstance, Version *byte, Length uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cVersion, _ := (*C.char)(unsafe.Pointer(Version)), cgoAllocsUnknown
	cLength, _ := (C.uint)(Length), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetVmDriverVersion(cVgpuInstance, cVersion, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetFbUsage function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetFbUsage(VgpuInstance VgpuInstance, FbUsage *uint64) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cFbUsage, _ := (*C.ulonglong)(unsafe.Pointer(FbUsage)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetFbUsage(cVgpuInstance, cFbUsage)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetLicenseStatus function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetLicenseStatus(VgpuInstance VgpuInstance, Licensed *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cLicensed, _ := (*C.uint)(unsafe.Pointer(Licensed)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetLicenseStatus(cVgpuInstance, cLicensed)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetType function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetType(VgpuInstance VgpuInstance, VgpuTypeId *VgpuTypeId) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cVgpuTypeId, _ := (*C.nvmlVgpuTypeId_t)(unsafe.Pointer(VgpuTypeId)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetType(cVgpuInstance, cVgpuTypeId)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetFrameRateLimit function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetFrameRateLimit(VgpuInstance VgpuInstance, FrameRateLimit *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cFrameRateLimit, _ := (*C.uint)(unsafe.Pointer(FrameRateLimit)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetFrameRateLimit(cVgpuInstance, cFrameRateLimit)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetEccMode function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetEccMode(VgpuInstance VgpuInstance, EccMode *EnableState) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cEccMode, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(EccMode)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetEccMode(cVgpuInstance, cEccMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetEncoderCapacity function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetEncoderCapacity(VgpuInstance VgpuInstance, EncoderCapacity *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cEncoderCapacity, _ := (*C.uint)(unsafe.Pointer(EncoderCapacity)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetEncoderCapacity(cVgpuInstance, cEncoderCapacity)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceSetEncoderCapacity function as declared in nvml/nvml.h
func nvmlVgpuInstanceSetEncoderCapacity(VgpuInstance VgpuInstance, EncoderCapacity uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cEncoderCapacity, _ := (C.uint)(EncoderCapacity), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceSetEncoderCapacity(cVgpuInstance, cEncoderCapacity)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetEncoderStats function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetEncoderStats(VgpuInstance VgpuInstance, SessionCount *uint32, AverageFps *uint32, AverageLatency *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cAverageFps, _ := (*C.uint)(unsafe.Pointer(AverageFps)), cgoAllocsUnknown
	cAverageLatency, _ := (*C.uint)(unsafe.Pointer(AverageLatency)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetEncoderStats(cVgpuInstance, cSessionCount, cAverageFps, cAverageLatency)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetEncoderSessions function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetEncoderSessions(VgpuInstance VgpuInstance, SessionCount *uint32, SessionInfo *EncoderSessionInfo) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cSessionInfo, _ := (*C.nvmlEncoderSessionInfo_t)(unsafe.Pointer(SessionInfo)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetEncoderSessions(cVgpuInstance, cSessionCount, cSessionInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetFBCStats function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetFBCStats(VgpuInstance VgpuInstance, FbcStats *FBCStats) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cFbcStats, _ := (*C.nvmlFBCStats_t)(unsafe.Pointer(FbcStats)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetFBCStats(cVgpuInstance, cFbcStats)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetFBCSessions function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetFBCSessions(VgpuInstance VgpuInstance, SessionCount *uint32, SessionInfo *FBCSessionInfo) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cSessionCount, _ := (*C.uint)(unsafe.Pointer(SessionCount)), cgoAllocsUnknown
	cSessionInfo, _ := (*C.nvmlFBCSessionInfo_t)(unsafe.Pointer(SessionInfo)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetFBCSessions(cVgpuInstance, cSessionCount, cSessionInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetGpuInstanceId function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetGpuInstanceId(VgpuInstance VgpuInstance, GpuInstanceId *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cGpuInstanceId, _ := (*C.uint)(unsafe.Pointer(GpuInstanceId)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetGpuInstanceId(cVgpuInstance, cGpuInstanceId)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetGpuPciId function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetGpuPciId(VgpuInstance VgpuInstance, VgpuPciId *byte, Length *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cVgpuPciId, _ := (*C.char)(unsafe.Pointer(VgpuPciId)), cgoAllocsUnknown
	cLength, _ := (*C.uint)(unsafe.Pointer(Length)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetGpuPciId(cVgpuInstance, cVgpuPciId, cLength)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetMetadata function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetMetadata(VgpuInstance VgpuInstance, nvmlVgpuMetadata *nvmlVgpuMetadata, BufferSize *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cnvmlVgpuMetadata, _ := (*C.nvmlVgpuMetadata_t)(unsafe.Pointer(nvmlVgpuMetadata)), cgoAllocsUnknown
	cBufferSize, _ := (*C.uint)(unsafe.Pointer(BufferSize)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetMetadata(cVgpuInstance, cnvmlVgpuMetadata, cBufferSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetVgpuMetadata function as declared in nvml/nvml.h
func nvmlDeviceGetVgpuMetadata(Device Device, PgpuMetadata *nvmlVgpuPgpuMetadata, BufferSize *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPgpuMetadata, _ := (*C.nvmlVgpuPgpuMetadata_t)(unsafe.Pointer(PgpuMetadata)), cgoAllocsUnknown
	cBufferSize, _ := (*C.uint)(unsafe.Pointer(BufferSize)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetVgpuMetadata(cDevice, cPgpuMetadata, cBufferSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlGetVgpuCompatibility function as declared in nvml/nvml.h
func nvmlGetVgpuCompatibility(nvmlVgpuMetadata *nvmlVgpuMetadata, PgpuMetadata *nvmlVgpuPgpuMetadata, CompatibilityInfo *VgpuPgpuCompatibility) Return {
	cnvmlVgpuMetadata, _ := (*C.nvmlVgpuMetadata_t)(unsafe.Pointer(nvmlVgpuMetadata)), cgoAllocsUnknown
	cPgpuMetadata, _ := (*C.nvmlVgpuPgpuMetadata_t)(unsafe.Pointer(PgpuMetadata)), cgoAllocsUnknown
	cCompatibilityInfo, _ := (*C.nvmlVgpuPgpuCompatibility_t)(unsafe.Pointer(CompatibilityInfo)), cgoAllocsUnknown
	__ret := C.nvmlGetVgpuCompatibility(cnvmlVgpuMetadata, cPgpuMetadata, cCompatibilityInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPgpuMetadataString function as declared in nvml/nvml.h
func nvmlDeviceGetPgpuMetadataString(Device Device, PgpuMetadata *byte, BufferSize *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPgpuMetadata, _ := (*C.char)(unsafe.Pointer(PgpuMetadata)), cgoAllocsUnknown
	cBufferSize, _ := (*C.uint)(unsafe.Pointer(BufferSize)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPgpuMetadataString(cDevice, cPgpuMetadata, cBufferSize)
	__v := (Return)(__ret)
	return __v
}

// nvmlGetVgpuVersion function as declared in nvml/nvml.h
func nvmlGetVgpuVersion(Supported *VgpuVersion, Current *VgpuVersion) Return {
	cSupported, _ := (*C.nvmlVgpuVersion_t)(unsafe.Pointer(Supported)), cgoAllocsUnknown
	cCurrent, _ := (*C.nvmlVgpuVersion_t)(unsafe.Pointer(Current)), cgoAllocsUnknown
	__ret := C.nvmlGetVgpuVersion(cSupported, cCurrent)
	__v := (Return)(__ret)
	return __v
}

// nvmlSetVgpuVersion function as declared in nvml/nvml.h
func nvmlSetVgpuVersion(VgpuVersion *VgpuVersion) Return {
	cVgpuVersion, _ := (*C.nvmlVgpuVersion_t)(unsafe.Pointer(VgpuVersion)), cgoAllocsUnknown
	__ret := C.nvmlSetVgpuVersion(cVgpuVersion)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetVgpuUtilization function as declared in nvml/nvml.h
func nvmlDeviceGetVgpuUtilization(Device Device, LastSeenTimeStamp uint64, SampleValType *ValueType, VgpuInstanceSamplesCount *uint32, UtilizationSamples *VgpuInstanceUtilizationSample) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLastSeenTimeStamp, _ := (C.ulonglong)(LastSeenTimeStamp), cgoAllocsUnknown
	cSampleValType, _ := (*C.nvmlValueType_t)(unsafe.Pointer(SampleValType)), cgoAllocsUnknown
	cVgpuInstanceSamplesCount, _ := (*C.uint)(unsafe.Pointer(VgpuInstanceSamplesCount)), cgoAllocsUnknown
	cUtilizationSamples, _ := (*C.nvmlVgpuInstanceUtilizationSample_t)(unsafe.Pointer(UtilizationSamples)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetVgpuUtilization(cDevice, cLastSeenTimeStamp, cSampleValType, cVgpuInstanceSamplesCount, cUtilizationSamples)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetVgpuProcessUtilization function as declared in nvml/nvml.h
func nvmlDeviceGetVgpuProcessUtilization(Device Device, LastSeenTimeStamp uint64, VgpuProcessSamplesCount *uint32, UtilizationSamples *VgpuProcessUtilizationSample) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLastSeenTimeStamp, _ := (C.ulonglong)(LastSeenTimeStamp), cgoAllocsUnknown
	cVgpuProcessSamplesCount, _ := (*C.uint)(unsafe.Pointer(VgpuProcessSamplesCount)), cgoAllocsUnknown
	cUtilizationSamples, _ := (*C.nvmlVgpuProcessUtilizationSample_t)(unsafe.Pointer(UtilizationSamples)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetVgpuProcessUtilization(cDevice, cLastSeenTimeStamp, cVgpuProcessSamplesCount, cUtilizationSamples)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetAccountingMode function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetAccountingMode(VgpuInstance VgpuInstance, Mode *EnableState) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cMode, _ := (*C.nvmlEnableState_t)(unsafe.Pointer(Mode)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetAccountingMode(cVgpuInstance, cMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetAccountingPids function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetAccountingPids(VgpuInstance VgpuInstance, Count *uint32, Pids *uint32) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	cPids, _ := (*C.uint)(unsafe.Pointer(Pids)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetAccountingPids(cVgpuInstance, cCount, cPids)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetAccountingStats function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetAccountingStats(VgpuInstance VgpuInstance, Pid uint32, Stats *AccountingStats) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cPid, _ := (C.uint)(Pid), cgoAllocsUnknown
	cStats, _ := (*C.nvmlAccountingStats_t)(unsafe.Pointer(Stats)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetAccountingStats(cVgpuInstance, cPid, cStats)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceClearAccountingPids function as declared in nvml/nvml.h
func nvmlVgpuInstanceClearAccountingPids(VgpuInstance VgpuInstance) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceClearAccountingPids(cVgpuInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetLicenseInfo_v2 function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetLicenseInfo_v2(VgpuInstance VgpuInstance, LicenseInfo *VgpuLicenseInfo) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cLicenseInfo, _ := (*C.nvmlVgpuLicenseInfo_t)(unsafe.Pointer(LicenseInfo)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetLicenseInfo_v2(cVgpuInstance, cLicenseInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlGetExcludedDeviceCount function as declared in nvml/nvml.h
func nvmlGetExcludedDeviceCount(DeviceCount *uint32) Return {
	cDeviceCount, _ := (*C.uint)(unsafe.Pointer(DeviceCount)), cgoAllocsUnknown
	__ret := C.nvmlGetExcludedDeviceCount(cDeviceCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlGetExcludedDeviceInfoByIndex function as declared in nvml/nvml.h
func nvmlGetExcludedDeviceInfoByIndex(Index uint32, Info *ExcludedDeviceInfo) Return {
	cIndex, _ := (C.uint)(Index), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlExcludedDeviceInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlGetExcludedDeviceInfoByIndex(cIndex, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceSetMigMode function as declared in nvml/nvml.h
func nvmlDeviceSetMigMode(Device Device, Mode uint32, ActivationStatus *Return) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cMode, _ := (C.uint)(Mode), cgoAllocsUnknown
	cActivationStatus, _ := (*C.nvmlReturn_t)(unsafe.Pointer(ActivationStatus)), cgoAllocsUnknown
	__ret := C.nvmlDeviceSetMigMode(cDevice, cMode, cActivationStatus)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMigMode function as declared in nvml/nvml.h
func nvmlDeviceGetMigMode(Device Device, CurrentMode *uint32, PendingMode *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCurrentMode, _ := (*C.uint)(unsafe.Pointer(CurrentMode)), cgoAllocsUnknown
	cPendingMode, _ := (*C.uint)(unsafe.Pointer(PendingMode)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMigMode(cDevice, cCurrentMode, cPendingMode)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstanceProfileInfo function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstanceProfileInfo(Device Device, Profile uint32, Info *GpuInstanceProfileInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfile, _ := (C.uint)(Profile), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlGpuInstanceProfileInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstanceProfileInfo(cDevice, cProfile, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstanceProfileInfoV function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstanceProfileInfoV(Device Device, Profile uint32, Info *GpuInstanceProfileInfo_v2) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfile, _ := (C.uint)(Profile), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlGpuInstanceProfileInfo_v2_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstanceProfileInfoV(cDevice, cProfile, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstancePossiblePlacements_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstancePossiblePlacements_v2(Device Device, ProfileId uint32, Placements *GpuInstancePlacement, Count *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cPlacements, _ := (*C.nvmlGpuInstancePlacement_t)(unsafe.Pointer(Placements)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstancePossiblePlacements_v2(cDevice, cProfileId, cPlacements, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstanceRemainingCapacity function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstanceRemainingCapacity(Device Device, ProfileId uint32, Count *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstanceRemainingCapacity(cDevice, cProfileId, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceCreateGpuInstance function as declared in nvml/nvml.h
func nvmlDeviceCreateGpuInstance(Device Device, ProfileId uint32, GpuInstance *GpuInstance) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cGpuInstance, _ := (*C.nvmlGpuInstance_t)(unsafe.Pointer(GpuInstance)), cgoAllocsUnknown
	__ret := C.nvmlDeviceCreateGpuInstance(cDevice, cProfileId, cGpuInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceCreateGpuInstanceWithPlacement function as declared in nvml/nvml.h
func nvmlDeviceCreateGpuInstanceWithPlacement(Device Device, ProfileId uint32, Placement *GpuInstancePlacement, GpuInstance *GpuInstance) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cPlacement, _ := (*C.nvmlGpuInstancePlacement_t)(unsafe.Pointer(Placement)), cgoAllocsUnknown
	cGpuInstance, _ := (*C.nvmlGpuInstance_t)(unsafe.Pointer(GpuInstance)), cgoAllocsUnknown
	__ret := C.nvmlDeviceCreateGpuInstanceWithPlacement(cDevice, cProfileId, cPlacement, cGpuInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceDestroy function as declared in nvml/nvml.h
func nvmlGpuInstanceDestroy(GpuInstance GpuInstance) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceDestroy(cGpuInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstances function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstances(Device Device, ProfileId uint32, GpuInstances *GpuInstance, Count *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cGpuInstances, _ := (*C.nvmlGpuInstance_t)(unsafe.Pointer(GpuInstances)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstances(cDevice, cProfileId, cGpuInstances, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstanceById function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstanceById(Device Device, Id uint32, GpuInstance *GpuInstance) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cId, _ := (C.uint)(Id), cgoAllocsUnknown
	cGpuInstance, _ := (*C.nvmlGpuInstance_t)(unsafe.Pointer(GpuInstance)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstanceById(cDevice, cId, cGpuInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetInfo function as declared in nvml/nvml.h
func nvmlGpuInstanceGetInfo(GpuInstance GpuInstance, Info *GpuInstanceInfo) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlGpuInstanceInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetInfo(cGpuInstance, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetComputeInstanceProfileInfo function as declared in nvml/nvml.h
func nvmlGpuInstanceGetComputeInstanceProfileInfo(GpuInstance GpuInstance, Profile uint32, EngProfile uint32, Info *ComputeInstanceProfileInfo) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cProfile, _ := (C.uint)(Profile), cgoAllocsUnknown
	cEngProfile, _ := (C.uint)(EngProfile), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlComputeInstanceProfileInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetComputeInstanceProfileInfo(cGpuInstance, cProfile, cEngProfile, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetComputeInstanceProfileInfoV function as declared in nvml/nvml.h
func nvmlGpuInstanceGetComputeInstanceProfileInfoV(GpuInstance GpuInstance, Profile uint32, EngProfile uint32, Info *ComputeInstanceProfileInfo_v2) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cProfile, _ := (C.uint)(Profile), cgoAllocsUnknown
	cEngProfile, _ := (C.uint)(EngProfile), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlComputeInstanceProfileInfo_v2_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetComputeInstanceProfileInfoV(cGpuInstance, cProfile, cEngProfile, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetComputeInstanceRemainingCapacity function as declared in nvml/nvml.h
func nvmlGpuInstanceGetComputeInstanceRemainingCapacity(GpuInstance GpuInstance, ProfileId uint32, Count *uint32) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetComputeInstanceRemainingCapacity(cGpuInstance, cProfileId, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceCreateComputeInstance function as declared in nvml/nvml.h
func nvmlGpuInstanceCreateComputeInstance(GpuInstance GpuInstance, ProfileId uint32, ComputeInstance *ComputeInstance) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cComputeInstance, _ := (*C.nvmlComputeInstance_t)(unsafe.Pointer(ComputeInstance)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceCreateComputeInstance(cGpuInstance, cProfileId, cComputeInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlComputeInstanceDestroy function as declared in nvml/nvml.h
func nvmlComputeInstanceDestroy(ComputeInstance ComputeInstance) Return {
	cComputeInstance, _ := *(*C.nvmlComputeInstance_t)(unsafe.Pointer(&ComputeInstance)), cgoAllocsUnknown
	__ret := C.nvmlComputeInstanceDestroy(cComputeInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetComputeInstances function as declared in nvml/nvml.h
func nvmlGpuInstanceGetComputeInstances(GpuInstance GpuInstance, ProfileId uint32, ComputeInstances *ComputeInstance, Count *uint32) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cComputeInstances, _ := (*C.nvmlComputeInstance_t)(unsafe.Pointer(ComputeInstances)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetComputeInstances(cGpuInstance, cProfileId, cComputeInstances, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlGpuInstanceGetComputeInstanceById function as declared in nvml/nvml.h
func nvmlGpuInstanceGetComputeInstanceById(GpuInstance GpuInstance, Id uint32, ComputeInstance *ComputeInstance) Return {
	cGpuInstance, _ := *(*C.nvmlGpuInstance_t)(unsafe.Pointer(&GpuInstance)), cgoAllocsUnknown
	cId, _ := (C.uint)(Id), cgoAllocsUnknown
	cComputeInstance, _ := (*C.nvmlComputeInstance_t)(unsafe.Pointer(ComputeInstance)), cgoAllocsUnknown
	__ret := C.nvmlGpuInstanceGetComputeInstanceById(cGpuInstance, cId, cComputeInstance)
	__v := (Return)(__ret)
	return __v
}

// nvmlComputeInstanceGetInfo_v2 function as declared in nvml/nvml.h
func nvmlComputeInstanceGetInfo_v2(ComputeInstance ComputeInstance, Info *ComputeInstanceInfo) Return {
	cComputeInstance, _ := *(*C.nvmlComputeInstance_t)(unsafe.Pointer(&ComputeInstance)), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlComputeInstanceInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlComputeInstanceGetInfo_v2(cComputeInstance, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceIsMigDeviceHandle function as declared in nvml/nvml.h
func nvmlDeviceIsMigDeviceHandle(Device Device, IsMigDevice *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIsMigDevice, _ := (*C.uint)(unsafe.Pointer(IsMigDevice)), cgoAllocsUnknown
	__ret := C.nvmlDeviceIsMigDeviceHandle(cDevice, cIsMigDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstanceId function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstanceId(Device Device, Id *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cId, _ := (*C.uint)(unsafe.Pointer(Id)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstanceId(cDevice, cId)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetComputeInstanceId function as declared in nvml/nvml.h
func nvmlDeviceGetComputeInstanceId(Device Device, Id *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cId, _ := (*C.uint)(unsafe.Pointer(Id)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetComputeInstanceId(cDevice, cId)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMaxMigDeviceCount function as declared in nvml/nvml.h
func nvmlDeviceGetMaxMigDeviceCount(Device Device, Count *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMaxMigDeviceCount(cDevice, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMigDeviceHandleByIndex function as declared in nvml/nvml.h
func nvmlDeviceGetMigDeviceHandleByIndex(Device Device, Index uint32, MigDevice *Device) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cIndex, _ := (C.uint)(Index), cgoAllocsUnknown
	cMigDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(MigDevice)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMigDeviceHandleByIndex(cDevice, cIndex, cMigDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetDeviceHandleFromMigDeviceHandle function as declared in nvml/nvml.h
func nvmlDeviceGetDeviceHandleFromMigDeviceHandle(MigDevice Device, Device *Device) Return {
	cMigDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&MigDevice)), cgoAllocsUnknown
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetDeviceHandleFromMigDeviceHandle(cMigDevice, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetBusType function as declared in nvml/nvml.h
func nvmlDeviceGetBusType(Device Device, _type *BusType) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	c_type, _ := (*C.nvmlBusType_t)(unsafe.Pointer(_type)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetBusType(cDevice, c_type)
	__v := (Return)(__ret)
	return __v
}

// nvmlInit_v1 function as declared in nvml/nvml.h
func nvmlInit_v1() Return {
	__ret := C.nvmlInit()
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetCount_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetCount_v1(DeviceCount *uint32) Return {
	cDeviceCount, _ := (*C.uint)(unsafe.Pointer(DeviceCount)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetCount(cDeviceCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleByIndex_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetHandleByIndex_v1(Index uint32, Device *Device) Return {
	cIndex, _ := (C.uint)(Index), cgoAllocsUnknown
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleByIndex(cIndex, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetHandleByPciBusId_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetHandleByPciBusId_v1(PciBusId string, Device *Device) Return {
	cPciBusId, _ := unpackPCharString(PciBusId)
	cDevice, _ := (*C.nvmlDevice_t)(unsafe.Pointer(Device)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetHandleByPciBusId(cPciBusId, cDevice)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPciInfo_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetPciInfo_v1(Device Device, Pci *PciInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPci, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(Pci)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPciInfo(cDevice, cPci)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetPciInfo_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetPciInfo_v2(Device Device, Pci *PciInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPci, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(Pci)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetPciInfo_v2(cDevice, cPci)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetNvLinkRemotePciInfo_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetNvLinkRemotePciInfo_v1(Device Device, Link uint32, Pci *PciInfo) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cLink, _ := (C.uint)(Link), cgoAllocsUnknown
	cPci, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(Pci)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetNvLinkRemotePciInfo(cDevice, cLink, cPci)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGridLicensableFeatures_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetGridLicensableFeatures_v1(Device Device, PGridLicensableFeatures *GridLicensableFeatures) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPGridLicensableFeatures, _ := (*C.nvmlGridLicensableFeatures_t)(unsafe.Pointer(PGridLicensableFeatures)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGridLicensableFeatures(cDevice, cPGridLicensableFeatures)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGridLicensableFeatures_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetGridLicensableFeatures_v2(Device Device, PGridLicensableFeatures *GridLicensableFeatures) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPGridLicensableFeatures, _ := (*C.nvmlGridLicensableFeatures_t)(unsafe.Pointer(PGridLicensableFeatures)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGridLicensableFeatures_v2(cDevice, cPGridLicensableFeatures)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGridLicensableFeatures_v3 function as declared in nvml/nvml.h
func nvmlDeviceGetGridLicensableFeatures_v3(Device Device, PGridLicensableFeatures *GridLicensableFeatures) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cPGridLicensableFeatures, _ := (*C.nvmlGridLicensableFeatures_t)(unsafe.Pointer(PGridLicensableFeatures)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGridLicensableFeatures_v3(cDevice, cPGridLicensableFeatures)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceRemoveGpu_v1 function as declared in nvml/nvml.h
func nvmlDeviceRemoveGpu_v1(PciInfo *PciInfo) Return {
	cPciInfo, _ := (*C.nvmlPciInfo_t)(unsafe.Pointer(PciInfo)), cgoAllocsUnknown
	__ret := C.nvmlDeviceRemoveGpu(cPciInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlEventSetWait_v1 function as declared in nvml/nvml.h
func nvmlEventSetWait_v1(Set EventSet, Data *EventData, Timeoutms uint32) Return {
	cSet, _ := *(*C.nvmlEventSet_t)(unsafe.Pointer(&Set)), cgoAllocsUnknown
	cData, _ := (*C.nvmlEventData_t)(unsafe.Pointer(Data)), cgoAllocsUnknown
	cTimeoutms, _ := (C.uint)(Timeoutms), cgoAllocsUnknown
	__ret := C.nvmlEventSetWait(cSet, cData, cTimeoutms)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetAttributes_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetAttributes_v1(Device Device, Attributes *DeviceAttributes) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cAttributes, _ := (*C.nvmlDeviceAttributes_t)(unsafe.Pointer(Attributes)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetAttributes(cDevice, cAttributes)
	__v := (Return)(__ret)
	return __v
}

// nvmlComputeInstanceGetInfo_v1 function as declared in nvml/nvml.h
func nvmlComputeInstanceGetInfo_v1(ComputeInstance ComputeInstance, Info *ComputeInstanceInfo) Return {
	cComputeInstance, _ := *(*C.nvmlComputeInstance_t)(unsafe.Pointer(&ComputeInstance)), cgoAllocsUnknown
	cInfo, _ := (*C.nvmlComputeInstanceInfo_t)(unsafe.Pointer(Info)), cgoAllocsUnknown
	__ret := C.nvmlComputeInstanceGetInfo(cComputeInstance, cInfo)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetComputeRunningProcesses_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetComputeRunningProcesses_v1(Device Device, InfoCount *uint32, Infos *ProcessInfo_v1) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v1_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetComputeRunningProcesses(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetComputeRunningProcesses_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetComputeRunningProcesses_v2(Device Device, InfoCount *uint32, Infos *ProcessInfo_v2) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v2_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetComputeRunningProcesses_v2(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGraphicsRunningProcesses_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetGraphicsRunningProcesses_v1(Device Device, InfoCount *uint32, Infos *ProcessInfo_v1) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v1_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGraphicsRunningProcesses(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGraphicsRunningProcesses_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetGraphicsRunningProcesses_v2(Device Device, InfoCount *uint32, Infos *ProcessInfo_v2) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v2_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGraphicsRunningProcesses_v2(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMPSComputeRunningProcesses_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetMPSComputeRunningProcesses_v1(Device Device, InfoCount *uint32, Infos *ProcessInfo_v1) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v1_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMPSComputeRunningProcesses(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetMPSComputeRunningProcesses_v2 function as declared in nvml/nvml.h
func nvmlDeviceGetMPSComputeRunningProcesses_v2(Device Device, InfoCount *uint32, Infos *ProcessInfo_v2) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cInfoCount, _ := (*C.uint)(unsafe.Pointer(InfoCount)), cgoAllocsUnknown
	cInfos, _ := (*C.nvmlProcessInfo_v2_t)(unsafe.Pointer(Infos)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetMPSComputeRunningProcesses_v2(cDevice, cInfoCount, cInfos)
	__v := (Return)(__ret)
	return __v
}

// nvmlDeviceGetGpuInstancePossiblePlacements_v1 function as declared in nvml/nvml.h
func nvmlDeviceGetGpuInstancePossiblePlacements_v1(Device Device, ProfileId uint32, Placements *GpuInstancePlacement, Count *uint32) Return {
	cDevice, _ := *(*C.nvmlDevice_t)(unsafe.Pointer(&Device)), cgoAllocsUnknown
	cProfileId, _ := (C.uint)(ProfileId), cgoAllocsUnknown
	cPlacements, _ := (*C.nvmlGpuInstancePlacement_t)(unsafe.Pointer(Placements)), cgoAllocsUnknown
	cCount, _ := (*C.uint)(unsafe.Pointer(Count)), cgoAllocsUnknown
	__ret := C.nvmlDeviceGetGpuInstancePossiblePlacements(cDevice, cProfileId, cPlacements, cCount)
	__v := (Return)(__ret)
	return __v
}

// nvmlVgpuInstanceGetLicenseInfo_v1 function as declared in nvml/nvml.h
func nvmlVgpuInstanceGetLicenseInfo_v1(VgpuInstance VgpuInstance, LicenseInfo *VgpuLicenseInfo) Return {
	cVgpuInstance, _ := (C.nvmlVgpuInstance_t)(VgpuInstance), cgoAllocsUnknown
	cLicenseInfo, _ := (*C.nvmlVgpuLicenseInfo_t)(unsafe.Pointer(LicenseInfo)), cgoAllocsUnknown
	__ret := C.nvmlVgpuInstanceGetLicenseInfo(cVgpuInstance, cLicenseInfo)
	__v := (Return)(__ret)
	return __v
}
